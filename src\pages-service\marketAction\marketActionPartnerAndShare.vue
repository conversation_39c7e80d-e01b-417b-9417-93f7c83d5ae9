<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '同行与分享',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="同行与分享" backRouteName="index" routeMethod="replaceAll">
        <wd-tabs v-model="tab" slidable="always" auto-line-width :lineHeight="5" color="blue" @change="changeTab">
            <block v-for="item in tabsList" :key="item">
                <wd-tab :title="`${item.name}`">
                    <!-- 我记录别人同行-------------------------------------------------------------- -->
                    <!-- 我记录别人同行-------------------------------------------------------------- -->
                    <!-- 我记录别人同行-------------------------------------------------------------- -->
                    <view scroll-y v-if="item.name == '我记录别人同行'">
                        <wd-search v-model="meRecordOtherSearchValue" @search="meRecordOtherChange"
                            @clear="meRecordOtherChange" :maxlength="30" hide-cancel placeholder="输入姓名搜索" />
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="meRecordOtherTable" :height="'70vh'" @row-click="meRecordOtherTableClick"
                                :fixed-header="false" :ellipsis="true" :rowHeight="50">
                                <!-- <wd-table-col prop="name" label="ID" :width="50" fixed></wd-table-col> -->
                                <wd-table-col prop="format_startTime" label="日期" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="clientName" label="客户" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="format_record" label="同行人" :width="colWidth">
                                    <template #value="{ row }">
                                        <view class="ellipsis" @click.stop="showText(row.format_record)">
                                            <text>{{ row.format_record }}</text>
                                        </view>
                                    </template>
                                </wd-table-col>
                                <wd-table-col prop="state_dictText" label="类别" :width="colWidth"></wd-table-col>
                            </wd-table>
                            <wd-pagination custom-style="border: 1px solid #ececec;border-top:none"
                                v-model="meRecordOtherPageNo" :pageSize="meRecordOtherPageSize"
                                :total="meRecordOtherTotal" @change="meRecordOtherPageChange"></wd-pagination>
                            <wd-status-tip v-if="meRecordOtherTable.length == 0" image="search" tip="当前搜索无结果" />
                        </view>
                    </view>
                    <!-- 别人记录我同行-------------------------------------------------------------- -->
                    <!-- 别人记录我同行-------------------------------------------------------------- -->
                    <!-- 别人记录我同行-------------------------------------------------------------- -->
                    <view scroll-y v-if="item.name == '别人记录我同行'">
                        <wd-search v-model="otherRecordMeSearchValue" @search="otherRecordMeChange"
                            @clear="otherRecordMeChange" :maxlength="30" hide-cancel placeholder="输入姓名搜索" />
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="otherRecordMeTable" :height="'70vh'" @row-click="otherRecordMeTableClick"
                                :fixed-header="false">
                                <wd-table-col prop="format_startTime" label="日期" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="clientName" label="客户" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="userName" label="记录人" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="state_dictText" label="类别" :width="colWidth"></wd-table-col>
                            </wd-table>
                            <wd-pagination custom-style="border: 1px solid #ececec;border-top:none"
                                v-model="otherRecordMePageNo" :pageSize="otherRecordMePageSize"
                                :total="otherRecordMeTotal" @change="otherRecordMePageChange"></wd-pagination>
                            <wd-status-tip v-if="otherRecordMeTable.length == 0" image="search" tip="当前搜索无结果" />
                        </view>
                    </view>
                    <!-- 我分享给别人-------------------------------------------------------------- -->
                    <!-- 我分享给别人-------------------------------------------------------------- -->
                    <!-- 我分享给别人-------------------------------------------------------------- -->
                    <view scroll-y v-if="item.name == '我分享给别人'">
                        <wd-search v-model="meShareOtherSearchValue" @search="meShareOtherChange"
                            @clear="meShareOtherChange" :maxlength="30" hide-cancel placeholder="输入姓名搜索" />
                        <view class="ml3 mr3 mb3">
                            <wd-table :data="meShareOtherTable" :height="'70vh'" @row-click="meShareOtherTableClick"
                                :fixed-header="false">
                                <wd-table-col prop="format_startTime" label="日期" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="clientName" label="客户" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="format_share" label="分享人" :width="colWidth">
                                    <template #value="{ row }">
                                        <view class="ellipsis" @click.stop="showText(row.format_share)">
                                            <text>{{ row.format_share }}</text>
                                        </view>
                                    </template>
                                </wd-table-col>
                                <wd-table-col prop="state_dictText" label="类别" :width="colWidth"></wd-table-col>
                            </wd-table>
                            <wd-pagination custom-style="border: 1px solid #ececec;border-top:none"
                                v-model="meShareOtherPageNo" :pageSize="meShareOtherPageSize" :total="meShareOtherTotal"
                                @change="meShareOtherPageChange"></wd-pagination>
                            <wd-status-tip v-if="meShareOtherTable.length == 0" image="search" tip="当前搜索无结果" />
                        </view>
                    </view>
                    <!-- 别人分享给我的-------------------------------------------------------------- -->
                    <!-- 别人分享给我的-------------------------------------------------------------- -->
                    <!-- 别人分享给我的-------------------------------------------------------------- -->
                    <view scroll-y v-if="item.name == '别人分享给我的'">
                        <wd-search v-model="otherShareMeSearchValue" @search="otherShareMeChange"
                            @clear="otherShareMeChange" :maxlength="30" hide-cancel placeholder="输入姓名搜索" />
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="otherShareMeTable" :height="'70vh'" @row-click="otherShareMeTableClick"
                                :fixed-header="false">
                                <wd-table-col prop="format_startTime" label="日期" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="clientName" label="客户" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="userName" label="分享" :width="colWidth"></wd-table-col>
                                <wd-table-col prop="state_dictText" label="类别" :width="colWidth"></wd-table-col>
                            </wd-table>
                            <wd-pagination custom-style="border: 1px solid #ececec;border-top:none"
                                v-model="otherShareMePageNo" :pageSize="otherShareMePageSize" :total="otherShareMeTotal"
                                @change="otherShareMePageChange"></wd-pagination>
                            <wd-status-tip v-if="otherShareMeTable.length == 0" image="search" tip="当前搜索无结果" />
                        </view>
                    </view>
                </wd-tab>
            </block>
        </wd-tabs>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './marketActionAddRecordFormConfig'
import { meRecordOther, otherRecordMe, meShareOther, otherShareMe } from '@/service/marketAction/marketAction'
import { encrypt } from '@/utils/crypto'
import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

const message = useMessage()

const colWidth = ref(100)
const tab = ref<number>(0)
const tabsList = [{
    name: '我记录别人同行'
}, {
    name: '别人记录我同行'
}, {
    name: '我分享给别人'
}, {
    name: '别人分享给我的'
}]
const changeTab = (e) => {
    console.log(e);
    switch (e.name) {
        case 0: return getMeRecordOther();
        case 1: return getOtherRecordMe();
        case 2: return getMeShareOther();
        case 3: return getOtherShareMe();
    }
}

const showText = (e) => {
    message.alert(e)
}

// 我记录别人同行====================================================
// 我记录别人同行====================================================
// 我记录别人同行====================================================
// 搜索
const meRecordOtherSearchValue = ref('')
const meRecordOtherChange = (e) => {
    console.log(e);
    getMeRecordOther()
}
// 表格-----------------------------
const meRecordOtherTable = ref([])
const meRecordOtherTableClick = (e) => {
    let data = meRecordOtherTable.value[e.rowIndex]
    console.log(data);
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: data.id
        }
    })
}
// 分页
const meRecordOtherPageNo = ref<number>(1)
const meRecordOtherPageSize = ref<number>(10)
const meRecordOtherTotal = ref<number>(meRecordOtherTable.value.length)
const meRecordOtherPageChange = (e) => {
    meRecordOtherPageNo.value = e.value
    getMeRecordOther()
}



// 别人记录我同行====================================================
// 别人记录我同行====================================================
// 别人记录我同行====================================================
// 搜索
const otherRecordMeSearchValue = ref('')
const otherRecordMeChange = (e) => {
    console.log(e);
    getOtherRecordMe()
}
// 表格
const otherRecordMeTable = ref([])
const otherRecordMeTableClick = (e) => {
    let data = otherRecordMeTable.value[e.rowIndex]
    console.log(data);
    let permission = null
    // #ifdef H5
    permission = encodeURIComponent(encrypt({
        add: false,
        edit: false,
        valid: false,
        share: false,
        finalSubmit: false
    }))
    // #endif
    // #ifdef MP-WEIXIN
    permission = encodeURIComponent(JSON.stringify({
        add: false,
        edit: false,
        valid: false,
        share: false,
        finalSubmit: false
    }))
    // #endif
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: data.id,
            p: permission
        }
    })
}
// 分页
const otherRecordMePageNo = ref<number>(1)
const otherRecordMePageSize = ref<number>(10)
const otherRecordMeTotal = ref<number>(otherRecordMeTable.value.length)
const otherRecordMePageChange = (e) => {
    otherRecordMePageNo.value = e.value
    getOtherRecordMe()
}

// 我分享给别人====================================================
// 我分享给别人====================================================
// 我分享给别人====================================================
// 搜索
const meShareOtherSearchValue = ref('')
const meShareOtherChange = (e) => {
    console.log(e);
}
// 表格
const meShareOtherTable = ref([])
const meShareOtherTableClick = (e) => {
    let data = meShareOtherTable.value[e.rowIndex]
    console.log(data);
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: data.id
        }
    })
}
// 分页
const meShareOtherPageNo = ref<number>(1)
const meShareOtherPageSize = ref<number>(10)
const meShareOtherTotal = ref<number>(meShareOtherTable.value.length)
const meShareOtherPageChange = (e) => {
    meShareOtherPageNo.value = e.value
    getMeShareOther()
}

// 群别人分享我====================================================
// 群别人分享我====================================================
// 群别人分享我====================================================
// 搜索
const otherShareMeSearchValue = ref('')
const otherShareMeChange = (e) => {
    console.log(e);
}
// 表格
const otherShareMeTable = ref([])
const otherShareMeTableClick = (e) => {
    let data = otherShareMeTable.value[e.rowIndex]
    console.log(data);
    let permission = null
    // #ifdef H5
    permission = encodeURIComponent(encrypt({
        add: false,
        edit: false,
        valid: false,
        share: false,
        finalSubmit: false
    }))
    // #endif
    // #ifdef MP-WEIXIN
    permission = encodeURIComponent(JSON.stringify({
        add: false,
        edit: false,
        valid: false,
        share: false,
        finalSubmit: false
    }))
    // #endif
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: data.id,
            p: permission
        }
    })
}
// 分页
const otherShareMePageNo = ref<number>(1)
const otherShareMePageSize = ref<number>(10)
const otherShareMeTotal = ref<number>(otherShareMeTable.value.length)
const otherShareMePageChange = (e) => {
    otherShareMePageNo.value = e.value
    getOtherShareMe()
}

const getMeRecordOther = () => {
    meRecordOther({
        key: meRecordOtherSearchValue.value,
        pageNo: meRecordOtherPageNo.value,
        pageSize: meRecordOtherPageSize.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            meRecordOtherTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD'),
                format_record: item.visitRecordsPartners ? item.visitRecordsPartners.map(item => item.userName).join('，') : ''
            }))
            meRecordOtherTotal.value = res.result.total
            console.log(meRecordOtherTable.value);

        }
    })
}
const getOtherRecordMe = () => {
    otherRecordMe({
        key: otherRecordMeSearchValue.value,
        pageNo: otherRecordMePageNo.value,
        pageSize: otherRecordMePageSize.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            otherRecordMeTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD')
            }))
            otherRecordMeTotal.value = res.result.total
        }
    })
}
const getMeShareOther = () => {
    meShareOther({
        key: meShareOtherSearchValue.value,
        pageNo: meShareOtherPageNo.value,
        pageSize: meShareOtherPageSize.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            meShareOtherTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD'),
                format_share: item.visitRecordsShares ? item.visitRecordsShares.map(item => item.userName).join('，') : ''
            }))
            meShareOtherTotal.value = res.result.total
        }
    })
}
const getOtherShareMe = () => {
    otherShareMe({
        key: meShareOtherSearchValue.value,
        pageNo: meShareOtherPageNo.value,
        pageSize: meShareOtherPageSize.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            otherShareMeTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD')
            }))
            otherShareMeTotal.value = res.result.total
        }
    })
}

// 初始化==========
onLoad((option) => {
    getMeRecordOther()
})

</script>

<style lang="scss" scoped>
.content {
    line-height: 120px;
    text-align: center;
}

::v-deep .wd-tabs__nav {
    height: 45px;
}


::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}

// {
//     overflow: hidden !important;
// }
.ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>