import { FormRules } from "wot-design-uni/components/wd-form/types"

import { isArray, isBoolean, isFunction, isNumber, isString } from '@/utils/is';

const formConfig = ref([
    {
        title: '',
        form: [
            {
                type: 'text',
                label: '创建人',
                name: 'custom_createBy'
            },
            {
                type: 'text',
                label: '创建日期',
                name: 'custom_createTime'
            },
            {
                type: 'picker',
                label: '同事分布的群组',
                name: 'custom_groupName',
                columns: [],
                defaultText: '请选择群组'
            },
            {
                type: 'text',
                label: '同事分布的群ID',
                name: 'groupId'
            },
            {
                type: 'text',
                label: '群主',
                name: 'groupOwnerName'
            },
            {
                type: 'text',
                label: '群主工号',
                name: 'groupOwnerAccount'
            },
            {
                type: 'picker',
                label: '群的督查人员',
                name: 'custom_groupManageName',
                columns: [],
                defaultText: '请选择人员'
            },
            {
                type: 'text',
                label: '群的督查人员工号',
                name: 'groupManageAccount'
            },
        ]
    }
])

const rules: FormRules = {
    custom_groupName: [
        {
            type: 'string',
            required: true,
            message: '请选择群',
            trigger: ['blur', 'change']
        }
    ],
    custom_groupManageName: [
        {
            required: true,
            validator: (value) => {
                if (!value) {
                    return false
                } else if (isArray(value) && value.length == 0) {
                    return false
                } else {
                    return true;
                }
            },
            message: '请选择审核人',
            trigger: ['change', 'blur'],
        }
    ],
}

export { formConfig, rules };