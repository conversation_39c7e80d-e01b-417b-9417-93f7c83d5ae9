<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '群的督查授权',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout navTitle="群的督查授权" backRouteName="index" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white">
                <wd-search v-model="searchGroupName" @search="getGroupAuthTable" @clear="getGroupAuthTable" hide-cancel custom-class="w45%"
                    placeholder="输入群名称搜索" />
                <wd-search v-model="searchPeopleName" @search="getGroupAuthTable" @clear="getGroupAuthTable" hide-cancel custom-class="w45%"
                    placeholder="输入督查人搜索" />
                <wd-icon name="add-circle" color="#0083ff" size="28px" custom-class="ml0" @click="toAdd" />
            </view>
            <view class="ml3 mr3 pb3">
                <wd-table :data="groupAuthTable" :height="'70vh'" @row-click="groupAuthTableClick"
                    :fixed-header="false">
                    <wd-table-col prop="groupName" label="群名称" :width="80" fixed></wd-table-col>
                    <wd-table-col prop="groupId" label="群ID" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="groupManageName" label="督查人" :width="80"></wd-table-col>
                    <wd-table-col prop="format_createTime" label="创建日期" :width="100"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="120">
                        <template #value="{ row }">
                            <wd-button size="small" custom-class="" @click.stop="toEdit(row)">修改</wd-button>
                            <wd-button type="error" size="small" custom-class="" @click.stop="toDel(row)">删除</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="groupAuthTablePageNo"
                    :pageSize="groupAuthTablePageSize" :total="groupAuthTableTotal"
                    @change="groupAuthTablePageChange"></wd-pagination>
                <wd-status-tip v-if="groupAuthTable.length == 0" image="search" tip="当前搜索无结果" />
            </view>
        </view>
    </PageLayout>
</template>
<script lang="ts" setup>
import { useRouter } from '@/plugin/uni-mini-router';
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { getMissionGroupAuthList, delMissionGroupAuth } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

const router = useRouter()
const colWidth = ref(50)


const searchGroupName = ref('')
const searchPeopleName = ref('')


// 表格
const groupAuthTable = ref([])
const groupAuthTableClick = (e) => {
    console.log(e);
    router.push({
        path: '/pages-service/mission/missionGroupAuthAdd',
        query: {
            action: 'check',
            id: groupAuthTable.value[e.rowIndex].id
        }
    })
}

// 分页
const groupAuthTablePageNo = ref<number>(1)
const groupAuthTablePageSize = ref<number>(10)
const groupAuthTableTotal = ref<number>(groupAuthTable.value.length)
const groupAuthTablePageChange = (e) => {
    groupAuthTablePageNo.value = e.value
    getGroupAuthTable()
}

// 查询
const getGroupAuthTable = () => {
    getMissionGroupAuthList({
        pageNo: groupAuthTablePageNo.value,
        pageSize: groupAuthTablePageSize.value,
        groupName: searchGroupName.value,
        groupManageName: searchPeopleName.value,
    }).then((res: any) => {
        console.log(res);
        groupAuthTable.value = res.result.records.map(item => ({
            ...item,
            format_createTime: dayjs(new Date(item.createTime).getTime()).format('YYYY-MM-DD')
        }))
    })
}

// 
const toAdd = (e) => {
    router.push({
        path: '/pages-service/mission/missionGroupAuthAdd',
        query: {
            action: 'add'
        }
    })
}
const toEdit = (e) => {
    console.log(e);
    router.push({
        path: '/pages-service/mission/missionGroupAuthAdd',
        query: {
            action: 'edit',
            id: e.id
        }
    })
}
const toDel = (e) => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delMissionGroupAuth({
                id: e.id
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    toast.success('删除成功!')
                    getGroupAuthTable()
                }
            })
        })
}

onLoad(() => {
    getGroupAuthTable()
    uni.$on('refreshList', (e) => {
        toast.success(e)
        getGroupAuthTable()
    })
})
onUnload(() => {
    uni.$off('refreshList')
})
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>