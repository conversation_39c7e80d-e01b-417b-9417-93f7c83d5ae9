<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '报销',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
  <PageLayout navTitle="报销" :showFab="false">
    <scroll-view class="p3" scroll-y>
      <view class="flex">
        <view class="mb3 bg-#aa6666 color-#ffffff w60% h7 line-height-7 text-center rd-1">{{ `报销日期：${currentDate}` }}
        </view>
        <!-- <view class="mb3 bg-#aa6666 color-#ffffff w45% h7 line-height-7 text-center rd-1 ml2">上传格式：pdf / jpg / png</view> -->
      </view>
      <wd-upload :file-list="uploadList" accept="all" multiple :action="action" :header="uploadHeader"
        image-mode="aspectFill" @change="handleSuccess" name="multipartFile" :before-preview="beforePreview">
      </wd-upload>
      <view class="mb3 color-red font-size-3.61">上传格式：pdf / jpg / png</view>
      <view class="mt3">
        <view v-for="(item, index) in uploadList" class="bg-white p1 rd-3 mb3 w90%">
          <!-- <wd-form :ref="`form${index}`" :model="item"> -->
          <wd-form ref="form" :model="item">
            <wd-cell-group border>
              <wd-picker :columns="costType" label="费用类型" v-model="item.costType" prop="costType"
                :rules="[{ required: true, message: '请选择费用类型' }]" />
              <wd-picker :columns="billType" label="发票类型" v-model="item.billType" prop="billType"
                :rules="[{ required: true, message: '请选择发票类型' }]" />
              <wd-input label="电子发票号" label-width="100px" prop="billCode" clearable v-model="item.billCode"
                placeholder="请输入电子发票号" :rules="[{ required: true, message: '请填写电子发票号' }]" />
              <wd-input label="金额" label-width="100px" prop="billAmount" clearable v-model="item.billAmount"
                placeholder="请输入金额" :rules="[{ required: true, message: '请填写金额' }]" />
              <view></view>
            </wd-cell-group>
          </wd-form>
        </view>
      </view>

      <wd-button type="success" @click="submitBX" custom-class="w90%">提交报销单</wd-button>
    </scroll-view>
  </PageLayout>
</template>
<script lang="ts" setup>
import { http } from '@/utils/http'
import { getDict } from '@/service/index/foo'
import { addReimburse } from '@/service/marketAction/marketAction'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const costType = ref([])
const billType = ref([])
getDict('costType').then((res: any) => {
  costType.value = res.result.map(item => ({
    value: Number(item.value),
    label: item.label
  }))
})
getDict('billType').then((res: any) => {
  billType.value = res.result.map(item => ({
    value: Number(item.value),
    label: item.label
  }))
})

const currentDate = ref('')

onLoad((opts) => {
  console.log(opts);
  currentDate.value = opts.date
})
onUnload(() => {
})

const uploadList = ref<any[]>([])

const form = ref()
const submitBX = async () => {
  // console.log(uploadList.value);
  let flag = true
  for (const i in form.value) {
    const ele = form.value[i];
    let res = await ele.validate()
    flag = flag && res.valid
  }
  console.log(flag);
  if (flag) {
    let param = uploadList.value.map(item => ({
      billAmount: item.billAmount,
      billCode: item.billCode,
      billType: item.billType,
      costType: item.costType,
      billUrl: item.billUrl,
      billJson: item.billJson,
      reimburseDate: currentDate.value,
      userId: userInfo.id,
      userName: userInfo.realname
    }))
    console.log(param);

    if (param.length == 0) {
      toast.error('请选择报销单')
      return
    }
    // 添加报销
    addReimburse(param).then((res: any) => {
      console.log(res);
      if (res.code == 200) {
        router.replace({
          path: '/pages-service/marketAction/marketActionReimburseList',
          query: {

          }
        })
      }
    })
  }
}

const handleSuccess = ({ fileList: files }) => {
  // console.log(file.fileList);
  setTimeout(() => {
    debounce(() => {
      console.log(files);
      let temp = uploadList.value
      uploadList.value = []
      console.log(temp);

      for (const i in files) {
        if (files[i].status == 'success' && files[i].response) {
          let res = JSON.parse(files[i].response)
          // console.log(files[i], res);

          if (res.result?.success) {
            let reimburse = res.result.visitReimburse
            console.log(res);

            let aaa = temp.find(item => item.name == files[i].name)
            if (aaa) {
              console.log(aaa);
              reimburse.billAmount = aaa.billAmount
              reimburse.billCode = aaa.billCode
              reimburse.billType = aaa.billType
              reimburse.costType = aaa.costType
              // reimburse.billUrl = aaa.billUrl
            }
            uploadList.value.push({
              ...files[i],
              billAmount: reimburse.billAmount,
              billCode: reimburse.billCode,
              billType: reimburse.billType,
              costType: reimburse.costType,
              billUrl: reimburse.billUrl,
              billJson: reimburse.billJson
            })

          } else {
            toast.error(res.message)
          }
        }
      }
    })
  }, 250);
}

let uploadHeader = http.uploadHeaderOptions()
// let ct = {
//   'Content-Type': 'application/json;charset=utf-8'
// }
// uploadHeader = { ...uploadHeader, ...ct }
const apiUrl = import.meta.env.VITE_SERVER_BASEURL
const action: string = `${apiUrl}/jeecg-action/action/visitReimburse/uploadsBill`
// const action: string = `https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload`

const beforePreview = ({ file, resolve }) => {
  console.log(file);
  resolve(true)
}

let timeout = null
const debounce = (func, wait = 500, immediate = false) => {
  // 清除定时器
  if (timeout !== null) clearTimeout(timeout)
  // 立即执行，此类情况一般用不到
  if (immediate) {
    const callNow = !timeout
    timeout = setTimeout(() => {
      timeout = null
    }, wait)
    if (callNow) typeof func === 'function' && func()
  } else {
    // 设置定时器，当最后一次操作后，timeout不会再被清除，所以在延时wait毫秒后执行func回调方法
    timeout = setTimeout(() => {
      typeof func === 'function' && func()
    }, wait)
  }
}

// let param = [
//   {
//     "billAmount": 200,
//     "billCode": "********",
//     "billType": 1,
//     "costType": 2,
//     "billUrl": "http://qn.gxcf.ltd/upload/bill2025/05/07/045002200111_********-副本-副本_1746581984266.pdf",
//     "billJson": "{\"type\":\"vat_invoice\",\"content\":{\"AmountInWords\":\"贰佰圆整\",\"TransportDeparture\":\"\",\"CommodityEndDate\":\"\",\"CommodityPrice\":\"6.57466568\",\"InvoiceTag\":\"成品油\",\"SellerRegisterNum\":\"91450103775990126P\",\"TransportPlateNum\":\"\",\"Remarks\":\"桂A5272L2024-10-0511:30:01广西南宁西乡塘区丰达站92号车用汽油(VIB)\",\"CommodityTaxRate\":\"13%\",\"ServiceType\":\"交通\",\"InvoiceCodeConfirm\":\"045002200111\",\"InvoiceCode\":\"045002200111\",\"TransportType\":\"\",\"PurchaserRegisterNum\":\"91450000718828878H\",\"InvoiceTypeOrg\":\"广西增值税电子普通发票\",\"Agent\":\"否\",\"AmountInFiguers\":\"200.00\",\"TransportArrival\":\"\",\"City\":\"\",\"CommodityAmount\":\"176.99\",\"PurchaserName\":\"广西田园生化股份有限公司\",\"CommodityType\":\"92#汽油\",\"Province\":\"广西壮族自治区\",\"PassengName\":\"\",\"PassengOrigin\":\"\",\"InvoiceNumDigit\":\"\",\"CommodityTax\":\"23.01\",\"PassengClass\":\"\",\"Payee\":\"黄楚婷\",\"PassengDestination\":\"\",\"InvoiceNumConfirm\":\"********\",\"CommodityStartDate\":\"\",\"CommodityVehicleType\":\"\",\"NoteDrawer\":\"邓德瑶\",\"SellerAddress\":\"南宁市青秀区玉兰路2号林业职工服务中心前属楼13层、服务管理用房(华格楼)二楼西边半层0771-6051767\",\"CommodityNum\":\"26.92\",\"MachineCode\":\"************\",\"SellerBank\":\"工商银行南宁市琅东支行2102112009300642460\",\"TotalTax\":\"23.01\",\"CheckCode\":\"73637222862714496472\",\"InvoiceDate\":\"2024年10月06日\",\"PassengDate\":\"\",\"Password\":\"06*139>+-/082>-0-908819>8>*/10<4<5544-+2443/9<<8/>1>28994+321>5>->********<82*888>878-32<4+2/46-+0*621/6819-\",\"OnlinePay\":\"\",\"PurchaserBank\":\"\",\"Checker\":\"赵彤\",\"PassengVehicleType\":\"\",\"TotalAmount\":\"176.99\",\"TransportCargoInfo\":\"\",\"InvoiceType\":\"电子普通发票\",\"SheetNum\":\"\",\"PurchaserAddress\":\"\",\"CommodityPlateNum\":\"\",\"CommodityUnit\":\"升\",\"CommodityName\":\"*汽油*92号车用汽油(VIB)\",\"SellerName\":\"中国石油天然气股份有限公司广西南宁销售分公司\",\"PassengIdNum\":\"\",\"InvoiceNum\":\"********\"}}",
//     "reimburseDate": "2025-05-01",
//     "userId": "1919920964966219778",
//     "userName": "龙勇"
//   }
// ]
</script>

<style lang="scss" scoped>
::v-deep .wd-upload__preview {
  width: 60px;
  height: 60px;
}

::v-deep .wd-upload__evoke {
  width: 60px;
  height: 60px;
}
</style>
