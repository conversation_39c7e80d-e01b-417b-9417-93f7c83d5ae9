<template>
    <!-- <view class=""> -->
    <wd-cell :title="label" :title-width="'80px'" :prop="prop">
        <template #default>
            <wd-card :title="`姓名：${defaultData.clientName || ''}`" custom-class="b-solid b-blue b-1 text-left">
                <view>
                    <view>联系方式：{{ defaultData.clientPhone || '' }}</view>
                    <!-- <view>拜访ID：{{ defaultData.clientName || '' }}</view> -->
                </view>
                <template #footer>
                    <wd-button size="small" plain @click="toSelClient" :disabled="disabled">选择客户信息</wd-button>
                </template>
            </wd-card>
        </template>
    </wd-cell>
    <!-- </view> -->
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Object,
        default: {
            clientName: '',
            clientPhone: ''
        }
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const toSelClient = () => {
    router.push({
        name: 'marketActionChooseExistedClient',
        params: {
            ...props.defaultData,
            name: props.name
        }
    })
}

</script>

<style lang="scss" scoped></style>