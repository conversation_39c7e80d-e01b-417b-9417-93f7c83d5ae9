<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '选择同行人',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
  <PageLayout navTitle="选择同行人">
    <wd-tabs v-model="tab" autoLineWidth @change="changeTab">
      <block v-for="item in tabs" :key="item">
        <wd-tab :title="`${item}`">
        </wd-tab>
      </block>
    </wd-tabs>

    <scroll-view scroll-y>
      <view v-if="tab == 0" class="pt3 pl3 pr3 pb3 bg-#ffffff">
        <wd-search v-model="searchCommonValue" hide-cancel @search="searchCommon" placeholder="输入姓名搜索" />
        <wd-checkbox-group v-model="commonValue" @change="changeCommon" :inline="false">
          <wd-checkbox v-for="item in commonPartners" v-model="item.id" size="large" shape="square">{{ item.label
          }}</wd-checkbox>
        </wd-checkbox-group>
      </view>
      <view v-if="tab == 1" class="pt3 pl3 pr3 pb3 bg-#ffffff">
        <wd-search v-model="searchValue" hide-cancel @search="search" placeholder="输入姓名搜索" />
        <wd-checkbox-group v-model="values" @change="change" :inline="false">
          <wd-checkbox v-for="item in partners" v-model="item.id" size="large" shape="square" :disabled="item.disabled">{{ item.label
          }}</wd-checkbox>
        </wd-checkbox-group>
      </view>
    </scroll-view>

    <view v-if="tab == 0" class="p3 flex flex-justify-around bg-#ffffff">
      <wd-button type="error" customClass="w45%" @click="delCommonPartners">取消常用</wd-button>
      <wd-button type="success" customClass="w45%" @click="selectCommon">选择同行人</wd-button>
    </view>
    <view v-if="tab == 1" class="p3 flex flex-justify-around bg-#ffffff">
      <wd-button type="warning" customClass="w45%" @click="addCommonPartners">设置常用</wd-button>
      <wd-button type="success" customClass="w45%" @click="selectCommon">选择同行人</wd-button>
    </view>
  </PageLayout>
</template>

<script lang="ts" setup>
import { getUserTenantPageList, addComUsedPartners, getComUsedPartners, checkComUsedRepeatByUserIds, delComUsedPartnersBatch } from '@/service/marketAction/marketAction'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const tab = ref<number>(0)
const tabs = ref(['常用同行人', '全部同行人'])
const changeTab = (e) => {
  console.log(e);
  if (e.name == 0) {
    getCommonPartners()
  }
}

const optsData = ref([])

onLoad(async (opts) => {
  await getCommonPartners()
  getAll()
  console.log(opts);
  if (opts.defaultData) {
    let data = JSON.parse(opts.defaultData)
    optsData.value = data
    console.log(commonPartners.value, data);
    let data2 = commonPartners.value.filter(aitem => data.some(bItem => bItem.id === aitem.id));
    commonValue.value = data2.map(item => item.id)
    let data3 = partners.value.filter(aitem => data.some(bItem => bItem.id === aitem.id));
    values.value = data3.map(item => item.id)
    // console.log(commonValue.value);
  }
})
// 所有同行人搜索值
const searchValue = ref('')
// 常用同行人搜索值
const searchCommonValue = ref('')
// 所有同行人indexs
const values = ref<number[]>([])
// 所有同行人列表
const partners = ref([])
// 常用同行人indexs
const commonValue = ref<number[]>([])
// 常用同行人列表
const commonPartners = ref([])
// 所有同行人搜索
const search = (e) => {
  console.log(e);
  getAll()
}
// 常用同行人搜索
const searchCommon = (e) => {
  console.log(e);
  toast.info('开发中')
}
// 添加常用同行人
const addCommonPartners = () => {
  checkComUsedRepeatByUserIds({
    userIds: values.value.join(',')
  }).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      let data = partners.value.filter(aitem => values.value.some(bItem => bItem === aitem.id));
      console.log(data);
      for (let i in data) {
        addComUsedPartners({
          userId: data[i].id,
          userName: data[i].name,
          userAccount: data[i].workNo
        }).then((res: any) => {
          console.log(res);
          if (res.code == 200) {
            toast.success(res.message)
          }
        })
      }
    } else if (res.code == 500) {
      // console.log(res.message);
      toast.warning(res.message)
    }
  })
}

// 删除常用同行人
const delCommonPartners = () => {
  // let data = commonPartners.value.filter(aitem => commonValue.value.some(bItem => bItem === aitem.id));
  // for (let i in data) {
  delComUsedPartnersBatch({
    ids: commonValue.value.join(',')
  }).then((res: any) => {
    if (res.code == 200) {
      toast.success('删除成功')
      getCommonPartners()
    }
  })
  // }
}

// 获取常用同行人
const getCommonPartners = () => {
  return new Promise((resolve, reject) => {
    getComUsedPartners({ pageSize: 1000 }).then((res: any) => {
      console.log(res);
      if (res.code == 200) {
        commonPartners.value = res.result.records.map(item => ({
          id: item.userId,
          label: `${item.userName} - ${item.userAccount}`,
          workNo: item.userAccount,
          name: item.userName
        }))
        resolve(null)
      }
    })
  })
}
// 选择常用的
const selectCommon = () => {
  let data = commonPartners.value.filter(aitem => commonValue.value.some(bItem => bItem === aitem.id));
  let data2 = partners.value.filter(aitem => values.value.some(bItem => bItem === aitem.id));
  let data3 = [...data, ...data2, ...optsData.value]

  console.log(data3);

  let map = new Map()
  data3.forEach(item => map.set(item.id, item))
  router.back()
  uni.$emit('choosePartners', Array.from(map.values()))
}
// 选择bu常用的
// const selectNotCommon = () => {
//   let data = partners.value.filter(aitem => values.value.some(bItem => bItem === aitem.id));
//   router.back()
//   uni.$emit('choosePartners', data)
// }

const changeCommon = (e) => {
  console.log(e);
}
const change = (e) => {
  console.log(e);
}

// 获取所有同行人
const getAll = () => {
  let data = {
    username: '',//工号
    realname: '',//姓名
    pageSize: 999,
    userTenantStatus: 1
  }
  if (searchValue.value) {
    data.realname = searchValue.value
    getUserTenantPageList(data).then((res: any) => {
      console.log(res);
      if (res.code == 200) {
        // res.result.records = res.result.records.filter(item => item.workNo != userInfo.workNo)
        partners.value = res.result.records.map(item => ({
          id: item.id,
          label: `${item.realname} - ${item.workNo}`,
          workNo: item.workNo,
          name: item.realname
        }))
        partners.value.find(item => item.workNo == userInfo.workNo).disabled = true
      }
    })
  }
}
</script>