<template>
    <!-- 顶部轮播图 -->
    <!-- <view class="h-48 overflow-hidden">
        <swiper class="h-full" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500"
            :circular="true">
            <swiper-item v-for="(item, index) in swiperList" :key="index">
                <view class="h-full relative flex items-center justify-center">
                    <image class="w-100% h-100%" :src="item" mode="aspectFill"></image>
                    <view class="absolute left-10 bottom-10">
                        <text class="text-lg font-semibold text-gray-900 block mb-2">123</text>
                        <text class="text-sm text-gray-700 block">456</text>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view> -->

    <!-- 功能按钮区域 -->
    <view class="mx-2.5 mt-4">
        <view class="flex justify-between items-center mb-3">
            <text class="text-lg font-semibold text-gray-900">功能服务</text>
        </view>
        <view class="grid grid-cols-4 gap-3">
            <view
                class="flex flex-col items-center px-2 py-2 rounded-2xl bg-white shadow-sm transition-all duration-300 active:scale-95 active:shadow-md"
                v-for="(item, index) in featureList" :key="index" @tap="onFeatureTap(item)">
                <view class="w-12 h-12 rounded-2xl flex items-center justify-center mb-2"
                    :style="{ backgroundColor: item.bgColor }">
                    <!-- <text class="text-2xl">{{ item.icon }}</text> -->
                    <image class="w-9 h-9 rounded-2xl" :src="item.img" mode="aspectFill"></image>
                </view>
                <text class="text-xs text-gray-900 text-center">{{ item.text }}</text>
            </view>
        </view>
    </view>

    <!-- 推荐内容区域 -->
    <!-- <view class="mx-5 mt-4">
        <view class="flex justify-between items-center mb-6">
            <text class="text-lg font-semibold text-gray-900">推荐内容</text>
            <text class="text-sm text-purple-600">更多</text>
        </view>
        <view class="flex flex-col gap-4">
            <view
                class="flex bg-white rounded-2xl p-6 shadow-sm transition-all duration-300 active:scale-98 active:shadow-lg"
                v-for="(item, index) in contentList" :key="index" @tap="onContentTap(item)">
                <image class="w-30 h-30 rounded-xl mr-5 flex-shrink-0" :src="item.image" mode="aspectFill"></image>
                <view class="flex-1 flex flex-col">
                    <text class="text-base font-semibold text-gray-900 mb-2 leading-snug">{{ item.title }}</text>
                    <text class="text-sm text-gray-600 mb-4 leading-snug">{{ item.desc }}</text>
                    <view class="flex justify-between mt-auto">
                        <text class="text-xs text-gray-500">{{ item.time }}</text>
                        <text class="text-xs text-gray-500">{{ item.views }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view> -->
</template>

<script lang="ts" setup>
import { useGlobalStore } from '@/store'
import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

const props = defineProps({
    menusProp: {
        type: Array,
        default: [],
    }
})

let colors = ['#FFF3E0', '#FCE4EC', '#E8F5E8', '#F3E5F5', '#E3F2FD', '#E0F2F1']

const featureList = computed(() => {
    return props.menusProp.map(item => ({
        text: item.name,
        menuPerms: item.menuPerms,
        img: item.selectUrl,
        bgColor: colors[Math.floor(Math.random() * colors.length)],
        to: item.to
    }))
})


const onFeatureTap = (item) => {
    console.log(item);

    router.push({
        path: item.to,
        // query: {
        //     serviceIndex: item.text
        // }
    })


    // uni.$emit('changeTab', {
    //     tab: '/service',
    //     service: item.menuPerms,
    //     serviceIndex: item.text
    // })
}

const contentList = ref([])
const onContentTap = (item) => {

}

const swiperList = ref([
    'https://qn.gxcf.ltd/source/backgroud.jpg',
    'https://qn.gxcf.ltd/source/backgroud.jpg',
    'https://qn.gxcf.ltd/source/backgroud.jpg'
])
</script>