<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '添加阶段任务',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout :navTitle="customTitle" :showFab="false">
        <scroll-view scroll-y>
            <customForm :formConfig="formConfig" :formData="formData" :rules="rules" @setFormData="setFormData"
                @customSetFormData="customSetFormData" @submit="submit" btnText1="提交" :disabledBtn="action == 'check'">
            </customForm>
        </scroll-view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './missionAssignAddFormConfig'
import { addMissionAssign, getMissionAssignListById, editMissionAssign, getMyCreateGroupList } from '@/service/marketAction/marketAction'

const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const action = ref('')
const customTitle = computed(() => {
    if (action.value == 'add') {
        return '添加阶段任务'
    } else if (action.value == 'edit') {
        return '修改阶段任务'
    } else if (action.value == 'check') {
        return '查看阶段任务'
    }
})

const formData = ref({
    custom_createBy: userInfo.realname,
    taskTime: Date.now(),
    // custom_createTime: dayjs(Date.now()).format('YYYY-MM-DD'),
    groupName: '',
    groupId: '',
})

// 原组件表单值改变后可进行其他操作--------------------------
const setFormData = (e) => {
    console.log(e);
    if (e.name == 'custom_group') {
        formData.value.groupId = e.data.selectedItems.value
        formData.value.groupName = e.data.selectedItems.label
    }
}
// 自定义组件表单值改变后可进行其他操作--------------------------
const customSetFormData = (e) => {
    console.log(e);
}

onLoad((opts) => {
    console.log(opts);
    action.value = opts.action

    // 查询群组
    getMyCreateGroupList({
        znkqUserId: userInfo.znkqId
    }).then((res: any) => {
        // console.log(res);
        if (res.code == 200) {
            formConfig.value[0].form[2].columns = res.result.map(item => ({
                value: Number(item.id),
                label: item.name
            }))
            // console.log(formConfig.value[0].form[2].columns);
        }
    })

    if (opts.action == 'edit') {
        getMissionAssignListById({ id: opts.id }).then((res: any) => {
            // console.log(res);
            if (res.code == 200) {
                res.result.custom_createBy = userInfo.realname
                res.result.custom_group = Number(res.result.groupId)
                formData.value = res.result
            }
        })
        setFormDisAbled(false)
    }
    if (opts.action == 'check') {
        getMissionAssignListById({ id: opts.id }).then((res: any) => {
            // console.log(res);
            if (res.code == 200) {
                res.result.custom_createBy = userInfo.realname
                res.result.custom_group = Number(res.result.groupId)
                formData.value = res.result
            }
        })
        setFormDisAbled(true)
        console.log(formConfig.value);
    } else {
        setFormDisAbled(false)
    }
})

const setFormDisAbled = (flag) => {
    formConfig.value[0].form = formConfig.value[0].form.map(item => ({
        ...item,
        disabled: flag
    }))
}

const submit = () => {
    if (action.value == 'add') {
        let data = JSON.parse(JSON.stringify(formData.value))
        data.taskTime = dayjs(data.taskTime).format('YYYY-MM')
        console.log(data);

        addMissionAssign(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '添加成功！')
            } else {
                toast.error(res.message)
            }
        })
    }

    if (action.value == 'edit') {
        let data = formData.value
        editMissionAssign(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '编辑成功！')
            } else {
                toast.error(res.message)
            }
        })
    }
}

</script>

<style lang="scss" scoped></style>