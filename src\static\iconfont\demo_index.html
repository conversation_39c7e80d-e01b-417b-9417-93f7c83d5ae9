<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4828283" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe8c0;</span>
                <div class="name">211铃铛-线性</div>
                <div class="code-name">&amp;#xe8c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe606;</span>
                <div class="name">流程管理</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xeca6;</span>
                <div class="name">声音开</div>
                <div class="code-name">&amp;#xeca6;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe61c;</span>
                <div class="name">邮箱</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe619;</span>
                <div class="name">工作台</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe62b;</span>
                <div class="name">图表-表格-简单表格</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe62d;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe62d;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe67f;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe67f;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe617;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe662;</span>
                <div class="name">新增</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe6b0;</span>
                <div class="name">缺省-头像</div>
                <div class="code-name">&amp;#xe6b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe65d;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe618;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe62c;</span>
                <div class="name">代码示例</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe601;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe600;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe6a6;</span>
                <div class="name">五角星</div>
                <div class="code-name">&amp;#xe6a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe74f;</span>
                <div class="name">时钟</div>
                <div class="code-name">&amp;#xe74f;</div>
              </li>
          
            <li class="dib">
              <span class="icon u-iconfont">&#xe605;</span>
                <div class="name">成功</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'u-iconfont';
  src: 
       url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
       url('iconfont.woff?t=1741315727689') format('woff'),
       url('iconfont.ttf?t=1741315727689') format('truetype'),
       url('iconfont.svg?t=1741315727689#u-iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.u-iconfont {
  font-family: "u-iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="u-iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"u-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-message"></span>
            <div class="name">
              211铃铛-线性
            </div>
            <div class="code-name">.u-icon-message
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-bpm"></span>
            <div class="name">
              流程管理
            </div>
            <div class="code-name">.u-icon-bpm
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-msg"></span>
            <div class="name">
              声音开
            </div>
            <div class="code-name">.u-icon-msg
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-email"></span>
            <div class="name">
              邮箱
            </div>
            <div class="code-name">.u-icon-email
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-tabbar-workHome"></span>
            <div class="name">
              工作台
            </div>
            <div class="code-name">.u-icon-tabbar-workHome
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-table"></span>
            <div class="name">
              图表-表格-简单表格
            </div>
            <div class="code-name">.u-icon-table
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-edit"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.u-icon-edit
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-filter"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.u-icon-filter
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-sort"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.u-icon-sort
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-add"></span>
            <div class="name">
              新增
            </div>
            <div class="code-name">.u-icon-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-quesheng-touxiang"></span>
            <div class="name">
              缺省-头像
            </div>
            <div class="code-name">.u-icon-quesheng-touxiang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-tabbar-home"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.u-icon-tabbar-home
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-tabbar-user"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.u-icon-tabbar-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-tabbar-demo"></span>
            <div class="name">
              代码示例
            </div>
            <div class="code-name">.u-icon-tabbar-demo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-tabbar-message"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.u-icon-tabbar-message
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-star"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.u-icon-star
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-star-fill"></span>
            <div class="name">
              五角星
            </div>
            <div class="code-name">.u-icon-star-fill
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-clock"></span>
            <div class="name">
              时钟
            </div>
            <div class="code-name">.u-icon-clock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon u-iconfont u-icon-success"></span>
            <div class="name">
              成功
            </div>
            <div class="code-name">.u-icon-success
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="u-iconfont u-icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            u-iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-message"></use>
                </svg>
                <div class="name">211铃铛-线性</div>
                <div class="code-name">#u-icon-message</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-bpm"></use>
                </svg>
                <div class="name">流程管理</div>
                <div class="code-name">#u-icon-bpm</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-msg"></use>
                </svg>
                <div class="name">声音开</div>
                <div class="code-name">#u-icon-msg</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-email"></use>
                </svg>
                <div class="name">邮箱</div>
                <div class="code-name">#u-icon-email</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-tabbar-workHome"></use>
                </svg>
                <div class="name">工作台</div>
                <div class="code-name">#u-icon-tabbar-workHome</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-table"></use>
                </svg>
                <div class="name">图表-表格-简单表格</div>
                <div class="code-name">#u-icon-table</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-edit"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#u-icon-edit</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-filter"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#u-icon-filter</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-sort"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#u-icon-sort</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-add"></use>
                </svg>
                <div class="name">新增</div>
                <div class="code-name">#u-icon-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-quesheng-touxiang"></use>
                </svg>
                <div class="name">缺省-头像</div>
                <div class="code-name">#u-icon-quesheng-touxiang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-tabbar-home"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#u-icon-tabbar-home</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-tabbar-user"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#u-icon-tabbar-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-tabbar-demo"></use>
                </svg>
                <div class="name">代码示例</div>
                <div class="code-name">#u-icon-tabbar-demo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-tabbar-message"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#u-icon-tabbar-message</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-star"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#u-icon-star</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-star-fill"></use>
                </svg>
                <div class="name">五角星</div>
                <div class="code-name">#u-icon-star-fill</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-clock"></use>
                </svg>
                <div class="name">时钟</div>
                <div class="code-name">#u-icon-clock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#u-icon-success"></use>
                </svg>
                <div class="name">成功</div>
                <div class="code-name">#u-icon-success</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
