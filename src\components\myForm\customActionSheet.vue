<template>
    <view>
        <wd-button @click="show = true">{{ actions[actionIndex].name }}</wd-button>
        <wd-action-sheet v-model="show" :actions="actions" @close="show = false" @select="select" />
    </view>
</template>

<script lang="ts" setup>
const show = ref(false)
const actionIndex = ref(0)
defineProps({
    actions: {
        type: Array,
        default: []
    }
})

const select = (e) => {
    console.log(e);
    
}
</script>