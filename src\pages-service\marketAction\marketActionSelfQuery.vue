<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '自主查询',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="自主查询" backRouteName="index" routeMethod="replaceAll">
        <wd-tabs v-model="tab" slidable="always" auto-line-width :lineHeight="5" @change="changeTab">
            <block v-for="item in tabsList" :key="item">
                <wd-tab :title="`${item.name}`">
                    <!-- 营销动作列表-------------------------------------------------------------- -->
                    <!-- 营销动作列表-------------------------------------------------------------- -->
                    <!-- 营销动作列表-------------------------------------------------------------- -->
                    <scroll-view scroll-y v-if="item.name == '营销动作列表'" class="pb3">
                        <view class="ml3 mr3 mt2 flex flex-justify-around">
                            <wd-calendar type="daterange" v-model="actionListDate" @confirm="confirmActionListDate"
                                use-default-slot customClass="w100%" :min-date="minDate" :max-date="maxDate">
                                <wd-button icon="calendar" plain customClass="w100%">
                                    {{ `${dayjs(actionListDate[0]).format('YYYY-MM-DD')} 至
                                    ${dayjs(actionListDate[1]).format('YYYY-MM-DD')}` }}
                                </wd-button>
                            </wd-calendar>
                        </view>
                        <view class="ml2 mr2 mt2 mb2 flex flex-justify-around">
                            <wd-picker :columns="actionTypeColumns" v-model="actionType" @confirm="confirmActionType"
                                use-default-slot>
                                <wd-button icon="search" plain>
                                    {{actionTypeColumns.find(item => Number(item.value) == actionType)?.label}}
                                </wd-button>
                            </wd-picker>
                            <view>
                                <wd-button icon="search" plain @click.stop="showSubmitState = true">
                                    {{submitStateActions.find(item => item.id == submitState).name}}
                                </wd-button>
                                <wd-action-sheet v-model="showSubmitState" :actions="submitStateActions"
                                    @close="showSubmitState = false" @select="selectSubmitState" />
                            </view>
                            <view>
                                <wd-button icon="search" plain @click.stop="showValidState = true">
                                    {{validStateActions.find(item => item.id == validState).name}}
                                </wd-button>
                                <wd-action-sheet v-model="showValidState" :actions="validStateActions"
                                    @close="showValidState = false" @select="selectValidState" />
                            </view>
                        </view>
                        <view class="ml3 mr3 pb3" v-if="actionListTable.length != 0">
                            <wd-table :data="actionListTable" :height="'66vh'" @row-click="actionListTableClick"
                                @sort-method="actionListSort" :fixed-header="true">
                                <!-- <wd-table-col prop="id" label="动作ID" fixed></wd-table-col> -->
                                <wd-table-col prop="clientName" label="客户姓名" fixed></wd-table-col>
                                <wd-table-col prop="visitType_dictText" label="动作类别"></wd-table-col>
                                <wd-table-col prop="format_startTime" label="动作日期"></wd-table-col>
                                <wd-table-col prop="state_dictText" label="提交状态"></wd-table-col>
                                <wd-table-col prop="auditStatus_dictText" label="审核状态"></wd-table-col>
                            </wd-table>
                            <wd-pagination custom-style="border: 1px solid #ececec;border-top:none"
                                v-model="actionListPage" :pageSize="actionListPageSize" :total="actionListTotal"
                                @change="actionListChangePage"></wd-pagination>
                        </view>
                        <view v-else>
                            <wd-status-tip image="search" tip="当前搜索无结果" />
                        </view>
                    </scroll-view>
                    <!-- 营销动作统计-------------------------------------------------------------- -->
                    <!-- 营销动作统计-------------------------------------------------------------- -->
                    <!-- 营销动作统计-------------------------------------------------------------- -->
                    <scroll-view scroll-y v-if="item.name == '营销动作统计'" class="pb3">
                        <view class="ml3 mr3 mt2 mb2 flex flex-justify-around">
                            <wd-calendar type="daterange" v-model="actionSumDate" @confirm="confirmActionSumDate"
                                use-default-slot customClass="w100%" :min-date="minDate" :max-date="maxDate">
                                <wd-button icon="calendar" plain customClass="w100%">
                                    {{ `${dayjs(actionSumDate[0]).format('YYYY-MM-DD')} 至
                                    ${dayjs(actionSumDate[1]).format('YYYY-MM-DD')}` }}
                                </wd-button>
                            </wd-calendar>
                        </view>
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="actionSumTable" :height="'70vh'" @row-click="actionSumTableClick"
                                :fixed-header="false">
                                <wd-table-col :width="'80px'" prop="title" label="" fixed></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="firstVisitNum" label="首次拜访"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="undealVisitNum" label="未重拜"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="dealVisitNum" label="已重拜"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="biologicalTestsNum"
                                    label="生测实验"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="modelFieldNum" label="示范田"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="orderMeetingNum" label="订货会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="observationMeetingNum"
                                    label="观摩会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="noteNum" label="工作笔记"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="pullTrainingNum" label="拉练"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="promotionalNum" label="站点促销"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="productPromotionNum"
                                    label="产品宣传"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="trainingNum" label="培训"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="regularMeetingNum" label="例会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="farmersWillNum" label="农民会"></wd-table-col>
                            </wd-table>
                        </view>
                    </scroll-view>
                    <!-- 我的活动统计-------------------------------------------------------------- -->
                    <!-- 我的活动统计-------------------------------------------------------------- -->
                    <!-- 我的活动统计-------------------------------------------------------------- -->
                    <scroll-view scroll-y v-if="item.name == '我的活动统计'" class="pb3 max-h90vh">
                        <view class="ml2 mr2 mt2 mb2 flex flex-justify-around">
                            <wd-picker :columns="myStttsGroupColumns" v-model="myStttsGroup"
                                @confirm="confirmMyStttsGroup" use-default-slot customClass="w45%">
                                <wd-button icon="search" plain customClass="w100%">
                                    {{myStttsGroupColumns.find(item => item.value == myStttsGroup)?.label}}
                                </wd-button>
                            </wd-picker>
                            <wd-calendar type="month" v-model="myStttsDate" use-default-slot
                                @confirm="confirmMyStttsDate" customClass="w45%" :min-date="minDate"
                                :max-date="maxDate">
                                <wd-button icon="calendar" plain customClass="w100%">
                                    {{ dayjs(myStttsDate).format('YYYY-MM') }}
                                </wd-button>
                            </wd-calendar>
                        </view>
                        <view class="text-center mb2 font-size-5 mt3">已审核的动作列表</view>
                        <view class="ml3 mr3 mb3">
                            <wd-table :data="myStttsTable1" :height="'70vh'" @row-click="myStttsTableClick"
                                :fixed-header="false">
                                <wd-table-col :width="'80px'" prop="userName" label="经理名称" fixed></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="firstVisitNum" label="首次拜访">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.firstVisitNum, 1)">
                                            {{ row.firstVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="undealVisitNum" label="未重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.undealVisitNum, 1)">
                                            {{ row.undealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="dealVisitNum" label="已重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.dealVisitNum, 1)">
                                            {{ row.dealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="biologicalTestsNum" label="生测实验">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.biologicalTestsNum, 1)">
                                            {{ row.biologicalTestsNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="modelFieldNum" label="示范田">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.modelFieldNum, 1)">
                                            {{ row.modelFieldNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="orderMeetingNum" label="订货会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.orderMeetingNum, 1)">
                                            {{ row.orderMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="observationMeetingNum" label="观摩会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.observationMeetingNum, 1)">
                                            {{ row.observationMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="noteNum" label="工作笔记">
                                    <template #value="{ row }">
                                        <wd-button type="text" @click.stop="toTab1Search(custom_visitType.noteNum, 1)">
                                            {{ row.noteNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="pullTrainingNum" label="拉练">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.pullTrainingNum, 1)">
                                            {{ row.pullTrainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="promotionalNum" label="站点促销">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.promotionalNum, 1)">
                                            {{ row.promotionalNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="productPromotionNum" label="产品宣传">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.productPromotionNum, 1)">
                                            {{ row.productPromotionNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="trainingNum" label="培训">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.trainingNum, 1)">
                                            {{ row.trainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="regularMeetingNum" label="例会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.regularMeetingNum, 1)">
                                            {{ row.regularMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="farmersWillNum" label="农民会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.farmersWillNum, 1)">
                                            {{ row.farmersWillNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                            </wd-table>
                        </view>
                        <view class="text-center mb2 font-size-5">未审核的动作列表</view>
                        <view class="ml3 mr3 mb3">
                            <wd-table :data="myStttsTable2" :height="'70vh'" @row-click="myStttsTableClick"
                                :fixed-header="false">
                                <wd-table-col :width="'80px'" prop="userName" label="经理名称" fixed></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="firstVisitNum" label="首次拜访">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.firstVisitNum, 0)">
                                            {{ row.firstVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="undealVisitNum" label="未重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.undealVisitNum, 0)">
                                            {{ row.undealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="dealVisitNum" label="已重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.dealVisitNum, 0)">
                                            {{ row.dealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="biologicalTestsNum" label="生测实验">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.biologicalTestsNum, 0)">
                                            {{ row.biologicalTestsNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="modelFieldNum" label="示范田">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.modelFieldNum, 0)">
                                            {{ row.modelFieldNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="orderMeetingNum" label="订货会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.orderMeetingNum, 0)">
                                            {{ row.orderMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="observationMeetingNum" label="观摩会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.observationMeetingNum, 0)">
                                            {{ row.observationMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="noteNum" label="工作笔记">
                                    <template #value="{ row }">
                                        <wd-button type="text" @click.stop="toTab1Search(custom_visitType.noteNum, 0)">
                                            {{ row.noteNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="pullTrainingNum" label="拉练">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.pullTrainingNum, 0)">
                                            {{ row.pullTrainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="promotionalNum" label="站点促销">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.promotionalNum, 0)">
                                            {{ row.promotionalNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="productPromotionNum" label="产品宣传">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.productPromotionNum, 0)">
                                            {{ row.productPromotionNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="trainingNum" label="培训">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.trainingNum, 0)">
                                            {{ row.trainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="regularMeetingNum" label="例会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.regularMeetingNum, 0)">
                                            {{ row.regularMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="farmersWillNum" label="农民会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toTab1Search(custom_visitType.farmersWillNum, 0)">
                                            {{ row.farmersWillNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                            </wd-table>
                        </view>
                        <view class="text-center mb2 font-size-5">未完成的动作任务列表</view>
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="myStttsTable3" :height="'70vh'" @row-click="myStttsTableClick"
                                :fixed-header="false">
                                <wd-table-col :width="'80px'" prop="userName" label="经理名称" fixed></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="firstVisitNum" label="首次拜访"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="undealVisitNum" label="未重拜"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="dealVisitNum" label="已重拜"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="biologicalTestsNum"
                                    label="生测实验"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="modelFieldNum" label="示范田"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="orderMeetingNum" label="订货会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="observationMeetingNum"
                                    label="观摩会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="noteNum" label="工作笔记"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="pullTrainingNum" label="拉练"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="promotionalNum" label="站点促销"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="productPromotionNum"
                                    label="产品宣传"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="trainingNum" label="培训"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="regularMeetingNum" label="例会"></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="farmersWillNum" label="农民会"></wd-table-col>
                            </wd-table>
                            <wd-notice-bar custom-class="mt2" v-if="workAssignTextTips" :text="workAssignTextTips"
                                prefix="warn-bold" />
                        </view>
                    </scroll-view>
                    <!-- 群营销动作统计-------------------------------------------------------------- -->
                    <!-- 群营销动作统计-------------------------------------------------------------- -->
                    <!-- 群营销动作统计-------------------------------------------------------------- -->
                    <scroll-view scroll-y v-if="item.name == '群营销动作统计'" class="pb3">
                        <view class="ml2 mr2 mt2 mb2 flex flex-justify-around">
                            <wd-picker :columns="groupActionGroupColumns" v-model="groupActionGroup"
                                @confirm="confirmGroupActionGroup" use-default-slot customClass="w40%">
                                <wd-button icon="search" plain custom-class="w100%" size="small">
                                    {{groupActionGroupColumns.find(item => item.value == groupActionGroup)?.label}}
                                </wd-button>
                            </wd-picker>
                            <wd-calendar type="month" v-model="groupActionDate" use-default-slot
                                @confirm="confirmGroupActionDate" customClass="w30%" :min-date="minDate"
                                :max-date="maxDate">
                                <wd-button icon="calendar" plain custom-class="w100%" size="small">
                                    {{ dayjs(groupActionDate).format('YYYY-MM') }}
                                </wd-button>
                            </wd-calendar>
                            <view>
                                <wd-button icon="search" plain @click.stop="showGroupTableType = true" size="small">
                                    {{groupTableTypeActions.find(item => item.id == groupTableType).name}}
                                </wd-button>
                                <wd-action-sheet v-model="showGroupTableType" :actions="groupTableTypeActions"
                                    @close="showGroupTableType = false" @select="selectGroupTableType" />
                            </view>
                        </view>
                        <view class="ml3 mr3 pb3">
                            <wd-table :data="groupActionTable" :height="'70vh'" @row-click="groupActionTableClick"
                                :fixed-header="false">
                                <wd-table-col :width="'80px'" prop="userName" label="经理名称" fixed></wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="firstVisitNum" label="首次拜访">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.firstVisitNum)">
                                            {{ row.firstVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="undealVisitNum" label="未重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.undealVisitNum)">
                                            {{ row.undealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="dealVisitNum" label="已重拜">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.dealVisitNum)">
                                            {{ row.dealVisitNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="biologicalTestsNum" label="生测实验">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.biologicalTestsNum)">
                                            {{ row.biologicalTestsNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="modelFieldNum" label="示范田">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.modelFieldNum)">
                                            {{ row.modelFieldNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="orderMeetingNum" label="订货会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.orderMeetingNum)">
                                            {{ row.orderMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="observationMeetingNum" label="观摩会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.observationMeetingNum)">
                                            {{ row.observationMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="noteNum" label="工作笔记">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.noteNum)">
                                            {{ row.noteNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="pullTrainingNum" label="拉练">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.pullTrainingNum)">
                                            {{ row.pullTrainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="promotionalNum" label="站点促销">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.promotionalNum)">
                                            {{ row.promotionalNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="productPromotionNum" label="产品宣传">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.productPromotionNum)">
                                            {{ row.productPromotionNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="trainingNum" label="培训">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.trainingNum)">
                                            {{ row.trainingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="regularMeetingNum" label="例会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.regularMeetingNum)">
                                            {{ row.regularMeetingNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                                <wd-table-col :width="smallColWidth" prop="farmersWillNum" label="农民会">
                                    <template #value="{ row }">
                                        <wd-button type="text"
                                            @click.stop="toMyValidList(row, custom_visitType.farmersWillNum)">
                                            {{ row.farmersWillNum }}
                                        </wd-button>
                                    </template>
                                </wd-table-col>
                            </wd-table>
                            <wd-notice-bar custom-class="mt2" v-if="workAssignTextTips && groupTableType == 1"
                                :text="workAssignTextTips" prefix="warn-bold" />
                        </view>

                    </scroll-view>
                </wd-tab>
            </block>
        </wd-tabs>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './marketActionAddRecordFormConfig'
import { getMyMarketActionList, getAllMarketActionStatics, getMyMarketActionStatics, getGroupMarketActionStatics, getMyGroupList, queryByGroupId } from '@/service/marketAction/marketAction'
import { getDict } from '@/service/index/foo'

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

const custom_visitType = {
    firstVisitNum: 1,
    undealVisitNum: 2,
    dealVisitNum: 3,
    biologicalTestsNum: 4,
    modelFieldNum: 5,
    orderMeetingNum: 6,
    observationMeetingNum: 7,
    noteNum: 8,
    pullTrainingNum: 9,
    promotionalNum: 10,
    productPromotionNum: 11,
    trainingNum: 12,
    regularMeetingNum: 13,
    farmersWillNum: 14
}

const tab = ref<number>(0)
const tabsList = [{
    name: '营销动作列表'
}, {
    name: '营销动作统计'
}, {
    name: '我的活动统计'
}, {
    name: '群营销动作统计'
}]
const changeTab = (e) => {
    console.log(e);
    // switch (e.index) {
    //     case 0: return getTable1();
    //     case 1: return getTable2();
    //     case 2: return getTable3();
    //     case 3: return getTable4();
    // }
}

// table列宽度
const smallColWidth = ref('50px')

// 营销动作列表========================================================================================================
// 营销动作列表========================================================================================================
// 营销动作列表========================================================================================================
// 日期(筛选)
const actionListDate = ref<number[]>([Date.now() - 30 * 24 * 3600 * 1000, Date.now()])
const confirmActionListDate = (e) => {
    console.log(e);
    getTable1()
}
// 动作类别(筛选)
const actionType = ref(0)
const actionTypeColumns = ref([{ value: '0', label: '动作类别' }])
getDict('visitType').then((res: any) => {
    if (res.code == 0) {
        res.result.unshift({ value: '0', label: '动作类别' })
        actionTypeColumns.value = res.result
    }
})
const confirmActionType = (e) => {
    console.log(e);
    actionType.value = e.value
    getTable1()
}
// 提交状态(筛选)
const showSubmitState = ref(false)
const submitState = ref(-1)
const submitStateActions = [
    { name: '提交状态', id: -1 },
    { name: '临时保存', id: 0 },
    { name: '最终提交', id: 1 }
]
const selectSubmitState = (e) => {
    console.log(e);
    submitState.value = e.item.id
    getTable1()
}
// 审核状态(筛选)
const showValidState = ref(false)
const validState = ref(-1)
const validStateActions = [
    { name: '审核状态', id: -1 },
    { name: '未审核', id: 0 },
    { name: '已审核', id: 1 }
]
const selectValidState = (e) => {
    console.log(e);
    validState.value = e.item.id
    getTable1()
}
// 表格-----------------------------
let actionListTable = ref([])
const actionListPage = ref<number>(1)
const actionListPageSize = ref<number>(10)
const actionListTotal = ref<number>(50)
const actionListChangePage = (e) => {
    console.log(e);
    actionListPage.value = e.value
    getTable1()
}
// 按页码和每页条数截取数据
// return dataList.value.slice((page.value - 1) * pageSize.value, page.value * pageSize.value)
const actionListTableClick = (e) => {
    console.log(e);
    // 跳转记录详情
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: actionListTable.value[e.rowIndex].id
        }
    })
}
const actionListSort = (e) => {
    console.log(e);
    // 还原----------
    if (e.sortDirection == 0) {

    }
    // 降序----------
    if (e.sortDirection == 1) {
        // actionListTable.value = actionListTable.reverse()
    }
    // 升序----------
    if (e.sortDirection == -1) {
        // actionListTable.value = actionListTable.reverse()
    }
}
// 查询营销动作列表11111111111111111111111111
const getTable1 = () => {
    getMyMarketActionList({
        startDate: dayjs(actionListDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(actionListDate.value[1]).format('YYYY-MM-DD'),
        visitType: actionType.value == 0 ? '' : actionType.value,
        state: submitState.value == -1 ? '' : submitState.value,
        auditStatus: validState.value == -1 ? '' : validState.value,
        pageNo: actionListPage.value,
        pageSize: actionListPageSize.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            actionListTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD')
            }))
            actionListTotal.value = res.result.total
        }
    })
}

// 营销动作统计========================================================================================================
// 营销动作统计========================================================================================================
// 营销动作统计========================================================================================================
// 日期(筛选)
const actionSumDate = ref<number[]>([Date.now() - 30 * 24 * 3600 * 1000, Date.now()])
const confirmActionSumDate = (e) => {
    console.log(e);
    getTable2()
}
// 表格
const actionSumTable = ref([])
const actionSumTableClick = (e) => {

}

// 我的活动统计========================================================================================================
// 我的活动统计========================================================================================================
// 我的活动统计========================================================================================================
// 选择群组(筛选)
const myStttsGroup = ref(0)
const myStttsGroupColumns = ref([])
// 查询群组
// getMyGroupList({
//     znkqUserId: userInfo.znkqId
// }).then((res: any) => {
//     console.log(res);
//     if (res.code == 200) {
//         let arr = res.result.map(item => ({
//             value: item.id,
//             label: item.name
//         }))
//         // arr.unshift({ value: 0, label: '请选择群组' })
//         myStttsGroupColumns.value = arr
//         myStttsGroup.value = arr[0].value
//         console.log(myStttsGroup.value);
//     }
// })
const confirmMyStttsGroup = (e) => {
    console.log(e);
    getTable3()
}
// 日期年月(筛选)
const myStttsDate = ref(Date.now())
const confirmMyStttsDate = (e) => {
    console.log(e);
    getTable3()
}
// 表格
const myStttsTable1 = ref([])
const myStttsTable2 = ref([])
const myStttsTable3 = ref([])
const myStttsTableClick = (e) => {
    console.log(e);
}
// 查询营销动作列表
const toTab1Search = (visitType, auditType) => {
    console.log(visitType, auditType);
    actionListDate.value = [myStttsDate.value - 30 * 24 * 3600 * 1000, myStttsDate.value]
    actionType.value = visitType
    validState.value = auditType
    tab.value = 0
    getTable1()
}

// 群营销动作统计========================================================================================================
// 群营销动作统计========================================================================================================
// 群营销动作统计========================================================================================================
// 选择群组(筛选)
const groupActionGroup = ref(0)
const groupActionGroupColumns = ref([])
// 查询群组
// getMyGroupList({
//     znkqUserId: userInfo.znkqId
// }).then((res: any) => {
//     console.log(res);
//     if (res.code == 200) {
//         let arr = res.result.map(item => ({
//             value: item.id,
//             label: item.name
//         }))
//         groupActionGroupColumns.value = arr
//         groupActionGroup.value = arr[0].value
//     }
// })
const confirmGroupActionGroup = (e) => {
    console.log(e);
    getTable4()
}
// 日期年月(筛选)
const groupActionDate = ref(Date.now())
const confirmGroupActionDate = (e) => {
    console.log(e);
    getTable4()
}
// 表格类型(筛选)
const showGroupTableType = ref(false)
const groupTableType = ref(0)
const groupTableTypeActions = [
    { name: '未达标', id: 0 },
    { name: '指标完成对比', id: 1 },
    { name: '已审核', id: 2 },
    { name: '未审核', id: 3 }
]
const selectGroupTableType = (e) => {
    console.log(e);
    groupTableType.value = e.item.id
    switch (e.index) {
        case 0: return groupActionTable.value = groupActionTable1.value;
        case 1: return groupActionTable.value = groupActionTable2.value;;
        case 2: return groupActionTable.value = groupActionTable3.value;
        case 3: return groupActionTable.value = groupActionTable4.value;
    }
}
// 表格
const groupActionTable = ref([])
const groupActionTable1 = ref([])
const groupActionTable2 = ref([])
const groupActionTable3 = ref([])
const groupActionTable4 = ref([])
const groupActionTableClick = (e) => {
    console.log(e);
}
// 提示
const workAssignTextTips = ref('')

// 
const toMyValidList = (e, visitType) => {
    console.log(e);

    router.push({
        path: '/pages-service/marketAction/marketActionPeopleActList',
        query: {
            groupId: String(groupActionGroup.value),
            userId: e.userId,
            userName: e.userName,
            date: String(groupActionDate.value),
            visitType: visitType,
            auditState: groupTableType.value == 2 ? '1' : '0'
        }
    })
}

// 初始化======================================================================
// 初始化======================================================================
// 初始化======================================================================
onLoad((option) => {
    console.log(option);
    if (Number(option.date)) {
        actionListDate.value = [Number(option.date), Number(option.date)]
    }
    // 查询群组
    getMyGroupList({
        znkqUserId: userInfo.znkqId
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            let arr = res.result.map(item => ({
                value: item.id,
                label: item.name
            }))
            groupActionGroupColumns.value = arr
            groupActionGroup.value = arr[0].value
            myStttsGroupColumns.value = arr
            myStttsGroup.value = arr[0].value
            getTable4()
        }
    })
    getTable1()
    getTable2()
    getTable3()
})

// 查询营销动作统计2222222222222222222222222222
const getTable2 = () => {
    getAllMarketActionStatics({
        startDate: dayjs(actionSumDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(actionSumDate.value[1]).format('YYYY-MM-DD'),
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            let data = res.result
            data.aggregateNum.title = '汇总数'
            data.auditPassNum.title = '已审'
            data.notAuditPassNum.title = '未审'
            data.draftStatusNum.title = '临时保存'
            data.submitStatusNum.title = '最终提交'
            let arr = []
            arr.push(data.submitStatusNum)
            arr.push(data.draftStatusNum)
            arr.push(data.notAuditPassNum)
            arr.push(data.auditPassNum)
            arr.push(data.aggregateNum)
            actionSumTable.value = arr
        }
    })
}
// 查询我的活动统计33333333333333333333333
const getTable3 = () => {
    getMyMarketActionStatics({
        date: dayjs(myStttsDate.value).format('YYYY-MM'),
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            myStttsTable1.value = [res.result.passData]
            myStttsTable2.value = [res.result.notAuditData]
            myStttsTable3.value = [res.result.unfinishedData]
        }
        queryByGroupId({
            groupId: myStttsGroup.value,
            date: dayjs(myStttsDate.value).format('YYYY-MM')
        }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                res.result.userName = '任务指标'
                res.result.firstVisitNum = res.result.firstVisitNum == null ? 0 : res.result.firstVisitNum
                res.result.undealVisitNum = res.result.undealVisitNum == null ? 0 : res.result.undealVisitNum
                res.result.dealVisitNum = res.result.dealVisitNum == null ? 0 : res.result.dealVisitNum
                res.result.biologicalTestsNum = res.result.biologicalTestsNum == null ? 0 : res.result.biologicalTestsNum
                res.result.modelFieldNum = res.result.modelFieldNum == null ? 0 : res.result.modelFieldNum
                res.result.orderMeetingNum = res.result.orderMeetingNum == null ? 0 : res.result.orderMeetingNum
                res.result.observationMeetingNum = res.result.observationMeetingNum == null ? 0 : res.result.observationMeetingNum
                res.result.noteNum = res.result.noteNum == null ? 0 : res.result.noteNum
                res.result.pullTrainingNum = res.result.pullTrainingNum == null ? 0 : res.result.pullTrainingNum
                res.result.promotionalNum = res.result.promotionalNum == null ? 0 : res.result.promotionalNum
                res.result.productPromotionNum = res.result.productPromotionNum == null ? 0 : res.result.productPromotionNum
                res.result.trainingNum = res.result.trainingNum == null ? 0 : res.result.trainingNum
                res.result.regularMeetingNum = res.result.regularMeetingNum == null ? 0 : res.result.regularMeetingNum
                res.result.farmersWillNum = res.result.farmersWillNum == null ? 0 : res.result.farmersWillNum
                myStttsTable3.value.unshift(res.result)

                workAssignTextTips.value = res.message
            }
        })
    })
}
// 群营销动作统计4444444444444444444444444444444
const getTable4 = () => {
    getGroupMarketActionStatics({
        date: dayjs(groupActionDate.value).format('YYYY-MM'),
        groupId: groupActionGroup.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            groupActionTable1.value = res.result.notUpToStandardDataList
            groupActionTable2.value = res.result.unfinishedDataList
            groupActionTable3.value = res.result.passDataList
            groupActionTable4.value = res.result.notAuditDataList
            groupActionTable.value = groupActionTable1.value

            if (groupActionTable2.value[0].userName == '任务指标') {
                groupActionTable2.value.shift()
            }
            // 获取任务指标====================
            queryByGroupId({
                groupId: groupActionGroup.value,
                date: dayjs(groupActionDate.value).format('YYYY-MM')
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    res.result.userName = '任务指标'
                    res.result.firstVisitNum = res.result.firstVisitNum == null ? 0 : res.result.firstVisitNum
                    res.result.undealVisitNum = res.result.undealVisitNum == null ? 0 : res.result.undealVisitNum
                    res.result.dealVisitNum = res.result.dealVisitNum == null ? 0 : res.result.dealVisitNum
                    res.result.biologicalTestsNum = res.result.biologicalTestsNum == null ? 0 : res.result.biologicalTestsNum
                    res.result.modelFieldNum = res.result.modelFieldNum == null ? 0 : res.result.modelFieldNum
                    res.result.orderMeetingNum = res.result.orderMeetingNum == null ? 0 : res.result.orderMeetingNum
                    res.result.observationMeetingNum = res.result.observationMeetingNum == null ? 0 : res.result.observationMeetingNum
                    res.result.noteNum = res.result.noteNum == null ? 0 : res.result.noteNum
                    res.result.pullTrainingNum = res.result.pullTrainingNum == null ? 0 : res.result.pullTrainingNum
                    res.result.promotionalNum = res.result.promotionalNum == null ? 0 : res.result.promotionalNum
                    res.result.productPromotionNum = res.result.productPromotionNum == null ? 0 : res.result.productPromotionNum
                    res.result.trainingNum = res.result.trainingNum == null ? 0 : res.result.trainingNum
                    res.result.regularMeetingNum = res.result.regularMeetingNum == null ? 0 : res.result.regularMeetingNum
                    res.result.farmersWillNum = res.result.farmersWillNum == null ? 0 : res.result.farmersWillNum
                    groupActionTable2.value.unshift(res.result)

                    workAssignTextTips.value = res.message

                }
                switch (groupTableType.value) {
                    case 0: return groupActionTable.value = groupActionTable1.value;
                    case 1: return groupActionTable.value = groupActionTable2.value;;
                    case 2: return groupActionTable.value = groupActionTable3.value;
                    case 3: return groupActionTable.value = groupActionTable4.value;
                }
            })
        }
    })
}

</script>

<style lang="scss" scoped>
.content {
    line-height: 120px;
    text-align: center;
}

::v-deep .wd-tabs__nav {
    height: 45px;
}


::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}

.wd-table {
    width: auto;
    border-radius: 8px;
}

.wd-table.is-border {
    border: 3px solid var(--wot-table-border-color, #48a4e0);
}

::v-deep .wd-table__cell.is-border {
    border-right: 1px solid var(--wot-table-border-color, #48a4e0);
    border-bottom: 1px solid var(--wot-table-border-color, #48a4e0);
}

// {
//     overflow: hidden !important;
// }</style>