<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '地图',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="我的活动地图" backRouteName="index" routeMethod="replaceAll" :showFab="false">


        <!-- H5----------------------------------- -->
        <!-- H5----------------------------------- -->
        <!-- H5----------------------------------- -->
        <!-- #ifdef H5 -->
        <view class="flex flex-justify-around flex-wrap p2 w100%" style="background-color: #ffffff;">
            <!-- <wd-calendar v-model="actionDate" label="" @confirm="getActions" use-default-slot custom-class="w45%">
                <wd-button customClass="w100% ml2 mr2">{{ dayjs(actionDate).format('YYYY-MM-DD') }}</wd-button>
            </wd-calendar> -->
            <wd-calendar type="daterange" v-model="actionDate" @confirm="getActions" use-default-slot
                customClass="w60% ml2 mr2 mb3" :min-date="minDate" :max-date="maxDate">
                <wd-button icon="calendar" plain customClass="w100%">
                    {{ `${dayjs(actionDate[0]).format('YYYY-MM-DD')} 至
                    ${dayjs(actionDate[1]).format('YYYY-MM-DD')}` }}
                </wd-button>
            </wd-calendar>

            <wd-button @click="showAction = true" custom-class="mb3" type="warning">
                {{ typeActions[typeActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showAction" @close="showAction = false" :actions="typeActions"
                @select="selectDisplayType" />

            <wd-button @click="showVisitAction = true" custom-class="w46%">
                {{ visitActions[visitActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showVisitAction" @close="showVisitAction = false" :actions="visitActions"
                @select="selectVisitAction" />

            <wd-button @click="showSubmitAction = true" custom-class="w46%">
                {{ sumbitActions[sumbitActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showSubmitAction" @close="showSubmitAction = false" :actions="sumbitActions"
                @select="selectSubmitAction" />
        </view>
        <!-- 地图 -->
        <view id="container" class="w100% h80vh"></view>
        <!-- #endif -->

        <!-- 微信-------------------------------------------- -->
        <!-- 微信-------------------------------------------- -->
        <!-- 微信-------------------------------------------- -->
        <!-- #ifdef MP-WEIXIN -->
        <view class="flex flex-justify-around flex-wrap p2 w100%" style="background-color: #ffffff;">
            <!-- <wd-calendar v-model="actionDate" label="" @confirm="getActions" use-default-slot custom-class="w45%">
                <wd-button customClass="w100% ml2 mr2">{{ dayjs(actionDate).format('YYYY-MM-DD') }}</wd-button>
            </wd-calendar> -->
            <wd-calendar type="daterange" v-model="actionDate" @confirm="getActionsWX" use-default-slot
                customClass="w60% ml2 mr2 mb3" :min-date="minDate" :max-date="maxDate">
                <wd-button icon="calendar" plain customClass="w100%">
                    {{ `${dayjs(actionDate[0]).format('YYYY-MM-DD')} 至
                    ${dayjs(actionDate[1]).format('YYYY-MM-DD')}` }}
                </wd-button>
            </wd-calendar>

            <wd-button @click="showAction = true" custom-class="mb3" type="warning">
                {{ typeActions[typeActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showAction" @close="showAction = false" :actions="typeActions"
                @select="selectDisplayTypeWX" />

            <wd-button @click="showVisitAction = true" custom-class="w46%">
                {{ visitActions[visitActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showVisitAction" @close="showVisitAction = false" :actions="visitActions"
                @select="selectVisitActionWX" />

            <wd-button @click="showSubmitAction = true" custom-class="w46%">
                {{ sumbitActions[sumbitActionIndex]?.name }}
            </wd-button>
            <wd-action-sheet v-model="showSubmitAction" @close="showSubmitAction = false" :actions="sumbitActions"
                @select="selectSubmitActionWX" />
        </view>
        <!-- 地图 -->
        <map class="w100% h75vh" id="wxmap" ref="wxmap" :latitude="latitudeWX" :longitude="longitudeWX"
            :markers="markersWX" @markertap="markerTap" @labeltap="labelTap" :scale="scaleWX" :circles="circlesWX"
            @callouttap="calloutTap" @tap="mapTap">
            <cover-view slot="callout">
                <cover-view class="" :marker-id="item.id" v-for="(item, index) in markersWX" :key="index">
                    <cover-view class="b-rd-3 bg-#ffffff color-lightblue p2 mb1">
                        {{ `${item.visitType_dictText}(${item.clientName})` }}
                    </cover-view>
                </cover-view>
            </cover-view>
            
            <!-- <cover-view class="absolute right-4 bottom-9 bg-gray-2">
                <cover-view class="p2 text-center font-size-6" @click="fandda">＋</cover-view>
                <cover-view class="pl2 pr2 pb1 text-center font-size-8" @click="suoxiao">-</cover-view>
            </cover-view> -->

            <cover-view class="absolute right-4 bottom-9 bg-#ffffff rd-2">
                <cover-view class="p2 text-center font-size-6 border-b-black border-b" @click="fandda">＋</cover-view>
                <cover-view class="pl2 pr2 pb1 text-center font-size-8" @click="suoxiao">-</cover-view>
            </cover-view>
        </map>
        <!-- #endif -->

        <!-- 弹窗----------------- -->
        <wd-popup v-model="showPop" position="bottom" @close="showPop = false">
            <scroll-view scroll-y class="mb8 mt8 pl2 pr2 max-h60">
                <wd-cell-group :border="true">
                    <wd-cell v-for="item in actionList" :title="`${item.clientName} - ${item.clientPhone}`" clickable
                        :value="item.visitType_dictText" @click="toSingleDetail(item)" />
                </wd-cell-group>
            </scroll-view>
            <!-- <view class="pb8 pt3 pl2 pr2">
                <wd-cell-group>
                    <wd-cell v-for="item in actionList" :title="item.clientName" clickable value="内容" />
                </wd-cell-group>
            </view> -->
        </wd-popup>
    </PageLayout>
</template>

<script lang="ts" setup>

import AMapLoader from '@amap/amap-jsapi-loader';
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router';
import { useUserStore } from '@/store';
import { getMyActionMap } from '@/service/marketAction/marketAction'
import { getDict } from '@/service/index/foo'

const router = useRouter()
const toast = useToast();
const userInfo = useUserStore().userInfo

// 展示
const showPop = ref(false)

// 地图点位数据
const origin_actionData = ref([])
const actionList = ref([])

// 查看单个
const toSingleDetail = (e) => {
    console.log(e);
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: e.id
        }
    })
}


// 日期筛选
const actionDate = ref<number[]>([Date.now() - 30 * 24 * 3600 * 1000, Date.now()])
const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

// 展示类型筛选111
const showAction = ref(false)
const typeActionIndex = ref(0)
const typeActions = ref([
    {
        name: '展示名称'
    },
    {
        name: '展示热力图'
    }
])
// 选择展示类型
const selectDisplayType = (e) => {
    console.log(e);
    typeActionIndex.value = e.index
    visitActionIndex.value = 0
    sumbitActionIndex.value = 0
    switch (e.index) {
        case 0: return getByclientName_clientPhone_visitType(origin_actionData.value);
        case 1: return getByPoints(origin_actionData.value);
    }
}

// 选择展示类型WX
const selectDisplayTypeWX = (e) => {
    console.log(e);
    typeActionIndex.value = e.index
    visitActionIndex.value = 0
    sumbitActionIndex.value = 0
    switch (e.index) {
        case 0: return getByclientName_clientPhone_visitTypeWX(origin_actionData.value);
        case 1: return getByPointsWX(origin_actionData.value);
    }
}

// 展示热力图
const showPopup = (e) => {
    let extData = e.target.getExtData()
    console.log(extData);
    actionList.value = extData.data.points
    showPop.value = true
}

// 展示热力图WX
const showPopupWX = (points) => {
    actionList.value = points
    showPop.value = true
}
// 展示类型筛选222
const showVisitAction = ref(false)
const visitActionIndex = ref(0)
const visitActions = ref([])
getDict('visitType').then((res: any) => {
    console.log(res);
    visitActions.value = res.result.map(item => ({
        id: Number(item.value),
        name: item.label
    }))
    visitActions.value.unshift({
        id: -1,
        name: '全部拜访类型'
    })
})
const selectVisitAction = (e) => {
    console.log(e);
    visitActionIndex.value = e.index
    typeActionIndex.value = 0
    getByclientName_clientPhone_visitType(origin_actionData.value)
}
const selectVisitActionWX = (e) => {
    console.log(e);
    visitActionIndex.value = e.index
    typeActionIndex.value = 0
    getByclientName_clientPhone_visitTypeWX(origin_actionData.value)
}
// 展示类型筛选333
const showSubmitAction = ref(false)
const sumbitActionIndex = ref(0)
const sumbitActions = ref([])
getDict('visitState').then((res: any) => {
    console.log(res);
    sumbitActions.value = res.result.map(item => ({
        id: Number(item.value),
        name: item.label
    }))
    sumbitActions.value.unshift({
        id: -1,
        name: '全部提交类型'
    })
})
const selectSubmitAction = (e) => {
    console.log(e);
    sumbitActionIndex.value = e.index
    typeActionIndex.value = 0
    getByclientName_clientPhone_visitTypeWX(origin_actionData.value)
}
const selectSubmitActionWX = (e) => {
    console.log(e);
    sumbitActionIndex.value = e.index
    typeActionIndex.value = 0
    getByclientName_clientPhone_visitTypeWX(origin_actionData.value)
}






























// #ifdef MP-WEIXIN


const scaleWX = ref(13)
const latitudeWX = ref(22.833900)
const longitudeWX = ref(108.313000)
const markersWX = ref([])
const circlesWX = ref([])
const wxmap = uni.createMapContext('wxmap')

const markerTap = (e) => {
    console.log(e, e.markerId);
    if (e.markerId < 10000) {
        toDetailWX(e.markerId)
    } else {
        // distance()
    }
}
const labelTap = (e) => {
    // console.log(e, e.markerId);
    if (e.markerId < 10000) {
        toDetailWX(e.markerId)
    } else {
        let point = markersWX.value.find(item => item.id == e.markerId)
        console.log(point);

        circlesWX.value.find(item => {

            let dis = distance(point.centroid.lat, point.centroid.lng, item.latitude, item.longitude)
            // console.log(dis);
            if (dis == 0) {
                console.log('在圆内', item);
                showPopupWX(item.points)
            }
        })
    }
}

const mapTap = (e) => {
    // console.log(e);
    circlesWX.value.find(item => {
        let dis = distance(e.detail.latitude, e.detail.longitude, item.latitude, item.longitude)
        // console.log(dis);
        if (dis * 1000 < item.radius) {
            console.log('在圆内', item);
            showPopupWX(item.points)
        }
    })
}

const calloutTap = (e) => {
    console.log(e);
    toDetailWX(e.markerId)
}

const toDetailWX = (id) => {
    let data = markersWX.value.find(item => item.id == id)
    console.log(data);

    let type = data.clientName_clientPhone_visitType.split('_')[2]
    let ids = JSON.stringify(data.data[0].points.map(item => item.id))
    router.push({
        path: 'pages-service/marketAction/marketActionVisitClientList',
        query: {
            visitType: type,
            ids: ids,
            date: JSON.stringify(actionDate.value),
            backRouteName: 'marketActionActionMap',
            routeMethod: 'back'
        }
    })
}

onLoad(async (opts) => {
    getActionsWX()
})

// 查询点位数据----------------------------------
const getActionsWX = () => {
    getMyActionMap({
        userId: userInfo.id,
        startDate: dayjs(actionDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(actionDate.value[1]).format('YYYY-MM-DD')
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            // 计算地图中心
            let center = calculateSphericalCentroid(res.result.map(item => ({
                lat: item.clientGateLat,
                lng: item.clientGateLng
            })))
            console.log(center);

            // 设置地图中心
            latitudeWX.value = center.lat
            longitudeWX.value = center.lng

            // 设置筛选条件（name-phone-type）
            origin_actionData.value = res.result.map(item => ({
                ...item,
                clientName_clientPhone_visitType: `${item.clientName}_${item.clientPhone}_${item.visitType}`
            }))

            visitActionIndex.value = 0
            sumbitActionIndex.value = 0
            getByclientName_clientPhone_visitTypeWX(origin_actionData.value)
            // getByPointsWX(origin_actionData.value)

        } else {
            toast.error(res.message)
        }
    })
}
// 按照 客户-电话-类型 进行分类
const getByclientName_clientPhone_visitTypeWX = (data) => {
    console.log(data);

    let visitType = visitActions.value[visitActionIndex.value].id
    let submitType = sumbitActions.value[sumbitActionIndex.value].id
    console.log(visitType, submitType);
    if (visitType != -1) {
        data = data.filter(item => item.visitType == visitType)
    }
    if (submitType != -1) {
        data = data.filter(item => item.state == submitType)
    }

    // 清除地图
    markersWX.value = []
    circlesWX.value = []

    let resres = data.reduce((acc, item) => {
        if (!acc[item.clientName_clientPhone_visitType]) {
            acc[item.clientName_clientPhone_visitType] = [];
        }
        acc[item.clientName_clientPhone_visitType].push(item);
        return acc;
    }, {});
    // console.log(resres);
    const resultArray = Object.entries(resres).map(([clientName_clientPhone_visitType, data]) => ({
        clientName_clientPhone_visitType,
        data: aggregatePoints(data, 0.5)
    }));
    console.log(resultArray);

    let id = 1
    for (var i = 0; i < resultArray.length; i++) {
        let pointsArray = resultArray[i].data
        for (var j = 0; j < pointsArray.length; j++) {
            let circles = pointsArray[j]
            let marker = {
                ...resultArray[i],
                visitType_dictText: circles.points[0].visitType_dictText,
                clientName: resultArray[i].clientName_clientPhone_visitType.split('_')[0],
                clientName_clientPhone_visitType: resultArray[i].clientName_clientPhone_visitType,
                id: id,
                longitude: circles.centroid.lng,
                latitude: circles.centroid.lat,
                width: 60,
                height: 60,
                iconPath: `/static/tra/${circles.points[0].visitType}.png`,
                label: {
                    content: `${circles.count}`,
                    anchorY: -45,
                    anchorX: -6,
                    fontSize: 18,
                    textAlign: 'center',
                    color: '#ffffff'
                },
                customCallout: {
                    display: "ALWAYS",
                    anchorX: 0,
                    anchorY: 0
                },
            }
            id++
            markersWX.value.push(marker)
        }
    }
    setTimeout(() => {
        setFitViewWX()
    }, 333);
}

// 点位直接聚合
const getByPointsWX = (data) => {

    // 清除地图
    markersWX.value = []
    circlesWX.value = []

    let resultArray = aggregatePoints(data, 1)
    console.log(resultArray);
    let id = 10000
    for (var i = 0; i < resultArray.length; i++) {

        let marker = {
            ...resultArray[i],
            id: id,
            longitude: resultArray[i].centroid.lng,
            latitude: resultArray[i].centroid.lat,
            width: 1,
            height: 1,
            iconPath: `/static/tra/pos.png`,
            label: {
                content: `动作${resultArray[i].count}`,
                anchorY: -12,
                anchorX: -12,
                fontSize: 13,
                textAlign: 'center',
                color: '#000000'
            },
        }
        id++
        markersWX.value.push(marker)

        let circle = {
            ...resultArray[i],
            longitude: resultArray[i].centroid.lng,
            latitude: resultArray[i].centroid.lat,
            fillColor: '#56c721aa',
            color: '#ffffff',
            radius: 360 + resultArray[i].count * 10
        }
        circlesWX.value.push(circle)
    }
}

const fandda = () => {
    if (scaleWX.value < 20 && scaleWX.value >= 3) {
        scaleWX.value++
    }
}
const suoxiao = () => {
    if (scaleWX.value > 3 && scaleWX.value <= 20) {
        scaleWX.value--
    }
}

const setFitViewWX = () => {
    console.log('setFitView', markersWX.value);
    wxmap.includePoints({
        points: markersWX.value,
        padding: [50, 100, 50, 100] // 上右下左的padding
    });
}

// #endif






























// ------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------

// #ifdef H5

// 地图
const map = ref(null);
const loaderAMap = ref(null);
const zoom = ref(12)
const latitude = ref(22.833900)
const longitude = ref(108.313000)

// market偏移
const mapMarkerOffsetX = ref(-20)
const mapMarkerOffsetY = ref(-80)
// 圆偏移
const circleMarkerOffsetX = ref(-40)
const circleMarkerOffsetY = ref(-15)

onLoad(async (opts) => {
    await initMap()
    getActions()
})

// 查询点位数据----------------------------------
const getActions = () => {
    getMyActionMap({
        userId: userInfo.id,
        startDate: dayjs(actionDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(actionDate.value[1]).format('YYYY-MM-DD')
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            // 计算地图中心
            let center = calculateSphericalCentroid(res.result.map(item => ({
                lat: item.clientGateLat,
                lng: item.clientGateLng
            })))
            console.log(center);
            map.value.setCenter([center.lng, center.lat])
            map.value.setFitView()

            // 设置筛选条件（name-phone-type）
            origin_actionData.value = res.result.map(item => ({
                ...item,
                clientName_clientPhone_visitType: `${item.clientName}_${item.clientPhone}_${item.visitType}`
            }))

            visitActionIndex.value = 0
            sumbitActionIndex.value = 0
            getByclientName_clientPhone_visitType(origin_actionData.value)
            // getByPoints(origin_actionData.value)

        } else {
            toast.error(res.message)
        }
    })
}

// 按照 客户-电话-类型 进行分类
const getByclientName_clientPhone_visitType = (data) => {
    console.log(data);

    let visitType = visitActions.value[visitActionIndex.value].id
    let submitType = sumbitActions.value[sumbitActionIndex.value].id
    console.log(visitType, submitType);
    if (visitType != -1) {
        data = data.filter(item => item.visitType == visitType)
    }
    if (submitType != -1) {
        data = data.filter(item => item.state == submitType)
    }


    map.value.clearMap()
    let resres = data.reduce((acc, item) => {
        if (!acc[item.clientName_clientPhone_visitType]) {
            acc[item.clientName_clientPhone_visitType] = [];
        }
        acc[item.clientName_clientPhone_visitType].push(item);
        return acc;
    }, {});
    // console.log(resres);
    const resultArray = Object.entries(resres).map(([clientName_clientPhone_visitType, data]) => ({
        clientName_clientPhone_visitType,
        data: aggregatePoints(data, 0.5)
    }));
    console.log(resultArray);


    let Amap = loaderAMap.value
    for (var i = 0; i < resultArray.length; i++) {
        let pointsArray = resultArray[i].data
        for (var j = 0; j < pointsArray.length; j++) {
            let circles = pointsArray[j]
            let textMarker = new Amap.Marker({
                extData: {
                    data: resultArray[i]
                },
                position: new Amap.LngLat(circles.centroid.lng, circles.centroid.lat),
                content: `<view 
                            class="flex items-end w200px h80px color-black font-size-3 bg-contain bg-no-repeat"
                          >
                            <view 
                                class="w40px h40px bg-contain bg-no-repeat center color-white" 
                                style="background-image: url('/h5/static/tra/${circles.points[0].visitType}.png')"
                            >
                                ${circles.count}
                            </view>
                            <view class="b-rd-3 bg-#ffffff color-lightblue p2 mb1">
                                ${circles.points[0].visitType_dictText}(${resultArray[i].clientName_clientPhone_visitType.split('_')[0]})
                            </view>
                          </view>`,
                offset: new Amap.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
            })
            textMarker.on('click', toDetail)
            map.value.add(textMarker)
        }
    }
}

// 点位直接聚合
const getByPoints = (data) => {
    map.value.clearMap()
    let resultArray = aggregatePoints(data, 0.1)
    console.log(resultArray);
    let Amap = loaderAMap.value
    for (var i = 0; i < resultArray.length; i++) {
        let textMarker = new Amap.Marker({
            extData: {
                data: resultArray[i]
            },
            position: new Amap.LngLat(resultArray[i].centroid.lng, resultArray[i].centroid.lat),
            content: `<view class="center w80px h30px color-dark font-size-3">
                            <view>动作${resultArray[i].count}</view>
                          </view>`,
            offset: new Amap.Pixel(circleMarkerOffsetX.value, circleMarkerOffsetY.value),
        })
        textMarker.on('click', showPopup)
        map.value.add(textMarker)

        let circleMarker = new Amap.CircleMarker({
            extData: {
                data: resultArray[i]
            },
            center: [resultArray[i].centroid.lng, resultArray[i].centroid.lat],
            radius: 20 + resultArray[i].count * 3,//3D视图下，CircleMarker半径不要超过64px
            strokeColor: 'white',
            strokeWeight: 2,
            strokeOpacity: 0.5,
            fillColor: '#56c721',
            fillOpacity: 0.6,
            zIndex: 10,
            bubble: true,
            cursor: 'pointer',
            clickable: true
        })
        circleMarker.on('click', showPopup)
        map.value.add(circleMarker)
    }
}

// 查看详情
const toDetail = (e) => {
    let extData = e.target.getExtData()
    console.log(extData);
    let type = extData.data.clientName_clientPhone_visitType.split('_')[2]
    let ids = JSON.stringify(extData.data.data[0].points.map(item => item.id))
    router.push({
        path: 'pages-service/marketAction/marketActionVisitClientList',
        query: {
            visitType: type,
            ids: ids,
            date: JSON.stringify(actionDate.value),
            backRouteName: 'marketActionActionMap',
            routeMethod: 'back'
        }
    })
}


// 地图---------------------------------------------------
const initMap = async () => {
    return new Promise((resolve, reject) => {
        (window as any)._AMapSecurityConfig = {
            securityJsCode: "b1b170ac8a0d56e04dad3478ea8f1f9d",
        };
        AMapLoader.load({
            key: "f8aabaa343608b6efdbf4c2e48e6d8b3", // 申请好的Web端开发者Key，首次调用 load 时必填
            version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
            plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        })
            .then((AMap) => {
                loaderAMap.value = AMap
                map.value = new AMap.Map("container", {
                    resizeEnable: true,
                    center: [longitude.value, latitude.value],
                    zoom: zoom.value
                });
                AMap.plugin('AMap.ToolBar', function () {
                    let toolbar = new AMap.ToolBar(); //缩放工具条实例化
                    map.value.addControl(toolbar); //添加控件
                });
                resolve(1)
            })
            .catch((e) => {
                console.log(e);
            });
    })
}

// #endif

// 计算两点距离
const distance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // 地球半径，单位km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
}

// 聚合点算法
const aggregatePoints = (points, maxDistance) => {
    const clusters = [];
    const visited = new Set();

    points.forEach((point, index) => {
        if (visited.has(index)) return;

        const cluster = {
            count: 1,
            points: [point],
            centroid: { lng: point.clientGateLng, lat: point.clientGateLat }
        };

        // 查找附近点
        for (let i = index + 1; i < points.length; i++) {
            if (visited.has(i)) continue;

            const dist = distance(
                point.clientGateLat, point.clientGateLng,
                points[i].clientGateLat, points[i].clientGateLng
            );

            if (dist <= maxDistance) {
                cluster.count++;
                cluster.points.push(points[i]);
                // 更新中心点
                cluster.centroid.lng += points[i].clientGateLng;
                cluster.centroid.lat += points[i].clientGateLat;
                visited.add(i);
            }
        }

        // 计算中心点
        cluster.centroid.lng /= cluster.count;
        cluster.centroid.lat /= cluster.count;
        clusters.push(cluster);
        visited.add(index);
    });

    return clusters;
}

// 计算地图中心点
const calculateSphericalCentroid = (points) => {
    if (!points || points.length === 0) return null;

    let x = 0, y = 0, z = 0;

    points.forEach(point => {
        // 将经纬度转换为弧度
        const latRad = point.lat * Math.PI / 180;
        const lngRad = point.lng * Math.PI / 180;

        // 转换为三维直角坐标
        x += Math.cos(latRad) * Math.cos(lngRad);
        y += Math.cos(latRad) * Math.sin(lngRad);
        z += Math.sin(latRad);
    });

    const numPoints = points.length;
    x /= numPoints;
    y /= numPoints;
    z /= numPoints;

    // 转换回经纬度
    const hyp = Math.sqrt(x * x + y * y);
    const latRad = Math.atan2(z, hyp);
    const lngRad = Math.atan2(y, x);

    return {
        lat: latRad * 180 / Math.PI,
        lng: lngRad * 180 / Math.PI
    };
}

</script>

<style lang="scss" scoped>
::v-deep .amap-logo {
    display: none !important;
}

::v-deep .amap-copyright {
    display: none !important;
}

::v-deep .textMarker {
    display: flex;
    width: 60px;
    height: 30px;
    color: #ff0000d3;
    background-color: azure;
}
</style>