<template>
    <!-- 顶部搜索栏 -->
    <!-- <view class="bg-white p-8 shadow-sm" :style="{paddingTop:statusBarHeight}"> -->
    <view class="bg-white px-4 pt12 pb-2 shadow-sm">
        <view class="flex items-center bg-gray-100 rounded-xl p-3">
            <!-- <image class="w-5 h-5 mr-3" src="/static/icons/search.svg" mode="aspectFit"></image> -->
            <wd-icon name="search" size="20px"></wd-icon>
            <input class="flex-1 bg-transparent text-sm text-gray-600" placeholder="搜索服务..." />
        </view>
    </view>

    <!-- 主体内容区域 -->
    <view class="flex h-content">
        <!-- 左侧大类导航 -->
        <view class="w-category bg-white border-r border-gray-100">
            <scroll-view class="h-full" :scroll-y="true" :show-scrollbar="false">
                <view class="category-item p-2 border-b border-gray-50 transition-all duration-300"
                    v-for="(category, index) in categories" :key="index"
                    :class="activeCategoryIndex === category.menuPerms ? 'bg-purple-50 border-r-3 border-purple-600' : ''"
                    @tap="selectCategory(category)">
                    <view class="flex flex-col items-center">
                        <image class="w-8 h-8 mb-2" :src="category.selectUrl" mode="aspectFit"></image>
                        <text class="text-xs text-center leading-tight"
                            :class="activeCategoryIndex === category.menuPerms ? 'text-purple-600 font-semibold' : 'text-gray-600'">{{
                                category.name }}</text>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 右侧小类内容 -->
        <view class="flex-1 bg-white">
            <scroll-view class="h-full" :scroll-y="true" :show-scrollbar="false">
                <view class="p-4">
                    <!-- 分类标题 -->
                    <view class="mb-4">
                        <text class="text-lg font-semibold text-gray-900">{{ currentCategory?.name }}</text>
                        <!-- <text class="text-sm text-gray-500 block mt-1">{{ currentCategory?.remark }}</text> -->
                    </view>

                    <!-- 服务列表 -->
                    <view class="space-y-3">
                        <view
                            class="service-item bg-gray-50 rounded-xl p-2 transition-all duration-300 active:scale-98 active:bg-gray-100"
                            v-for="(service, index) in currentCategory?.children" :key="index"
                            @tap="selectService(service)">
                            <view class="flex items-center">
                                <view class="w-10 h-10 rounded-xl flex items-center justify-center mr-2"
                                    :style="{ backgroundColor: colors[Math.floor(Math.random() * colors.length)] }">
                                    <image class="w-8 h-8" :src="service.selectUrl" mode="aspectFit"></image>
                                    <!-- <image class="w-6 h-6" src="/static/index/128/yingxiaodongzuo.png" mode="aspectFit">
                                    </image> -->
                                </view>
                                <view class="flex-1">
                                    <text class="text-base font-semibold text-gray-900 block mb-1">{{
                                        service?.name }}</text>
                                    <text class="text-sm text-gray-500">{{ service?.remark }}</text>
                                </view>
                                <!-- <view class="flex flex-col items-end">
                                    <text class="text-sm font-semibold text-purple-600" v-if="service.price">¥{{
                                        service.price }}</text>
                                    <text class="text-xs text-gray-400" v-if="service.unit">{{ service.unit }}</text>
                                </view> -->
                            </view>
                            <!-- 服务标签 -->
                            <!-- <view class="flex flex-wrap mt-3 gap-2" v-if="service.tags">
                                <view class="tag px-2 py-1 rounded-md text-xs" v-for="(tag, tagIndex) in service.tags"
                                    :key="tagIndex"
                                    :class="tag.type === 'hot' ? 'bg-red-100 text-red-600' : tag.type === 'new' ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'">
                                    {{ tag.text }}
                                </view>
                            </view> -->
                        </view>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>


<script lang="ts" setup>
import { ref, nextTick } from 'vue'
// 获取当前运行平台
import { cache, getFileAccessHttpUrl, hasRoute } from '@/common/uitls'
import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useGlobalStore } from '@/store'

const globalData = getApp().globalData
const { systemInfo, navHeight } = globalData
const { statusBarHeight } = systemInfo
// console.log(statusBarHeight);


import { useToast, useMessage, useNotify } from 'wot-design-uni'
const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

const props = defineProps({
    menusProp: {
        type: Array,
        default: [],
    },
    catIndexProp: {
        type: String,
        default: '',
    }
})

let colors = ['#FFF3E0', '#FCE4EC', '#E8F5E8', '#F3E5F5', '#E3F2FD', '#E0F2F1']

const categories = computed(() => {
    return props.menusProp
})

// 当前选择的分类
const activeCategoryIndex = ref('/service/actionCost')
const selectCategory = (category) => {
    activeCategoryIndex.value = category.menuPerms;
}
const currentCategory = computed(() => {
    return categories.value.find(item => item.menuPerms == activeCategoryIndex.value) || categories.value[0]
    // categories.value[activeCategoryIndex.value] || categories.value[0];
})

// 监听----------------------
watch(
    () => props.catIndexProp,
    (val) => {
        activeCategoryIndex.value = val
    },
    { deep: true, immediate: true },
)

// 选择按钮
const selectService = (service) => {
    console.log(service);
    let url = toArr.value.find(item => item.perm == service.menuPerms).to
    console.log(url);

    router.push({ name: url, params: { backRouteName: 'index' } })
}


// const goPage = (item) => {
//     let page = item.routeIndex
//     console.log('-----------page------------', page)

//     if (!hasRoute({ name: page })) {
//         toast.info('该功能暂未实现')
//     } else {
//         router.push({ name: page, params: { backRouteName: 'index' } })
//     }
// }

const toArr = ref([
    {
        perm: "/service/actionCost/addMarketActionRecord",//记录动作
        to: "marketActionAddRecordMap"
    },
    {
        perm: "/service/actionCost/selfQueryActionRecord",//自助查询
        to: "marketActionSelfQuery"
    }, {
        perm: "/service/actionCost/myRecordMap",//活动地图
        to: "marketActionActionMap"
    }, {
        perm: "/service/actionCost/partnerAndShare",//同行分享
        to: "marketActionPartnerAndShare"
    }, {
        perm: "/service/actionCost/recordMemo",//备忘录
        to: "marketActionVisitClientList"
    }, {
        perm: "/service/actionCost/taskAllocation",//阶段任务下达
        to: "missionAssign"
    }, {
        perm: "/service/actionCost/auditAuth",//督查授权
        to: "missionGroupAuth"
    }, {
        perm: "/service/actionCost/recordAudit",//内勤审核
        to: "marketActionValidRecordList"
    }, {
        perm: "/service/actionCost/visitReimburse",//活动报账明细
        to: "marketActionReimburseList"
    }, {
        perm: "/service/actionCost/visitRestFiling",//休息备案
        to: "marketActionRestRegister"
    }, {
        perm: "/service/actionCost/visitRestFilingValid",//休息备案
        to: "marketActionRestRegisterValid"
    }, {
        perm: "/service/actionCost/stayInfo",//住宿补贴
        to: "marketActionLiveSubsidy"
    },
])

</script>

<style lang="scss" scoped>
.h-content {
    height: calc(100vh - 365rpx);
}
</style>