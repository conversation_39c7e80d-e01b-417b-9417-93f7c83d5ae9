<script lang="ts">
import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { beforEach } from '@/router/index'
export default {
  onLaunch: function (options) {
    // console.log('App Launch')
    // console.log('应用启动路径：', options.path)
    // setTimeout(() => {
    //   uni.hideTabBar()
    // }, 1000);
    

    // #ifdef MP-WEIXIN
    // 仅适用于微信小程序环境---ios兼容播放音频
    if (uni.getSystemInfoSync().platform === 'ios') {
        wx.setInnerAudioOption({
            obeyMuteSwitch: false,
            success: () => console.log('已允许静音模式下播放'),
            fail: (err) => console.log('设置失败', err)
        });
    }
    // #endif


  },
  onShow: function (options) {
    // console.log('App Show')
    // console.log('应用启动路径：', options.path)
    // 首次进入页面时路由拦截
    setTimeout(() => {
      const currentPage = options.path
      beforEach({ path: '/' }, { path: currentPage, fullPath: currentPage }, (data) => {
        if (data?.path) {
          uni.redirectTo({ url: data.path })
        }
      })
    }, 100)
  },
  onHide: function () {
    // console.log('App Hide')
  },
  // 全局变量
  globalData: {
    isLocalConfig: true,
    systemInfo: uni.getSystemInfoSync(),
    navHeight: 44,
  },
}
</script>

<style lang="scss">
body {
  font-size: 14px;
  color: #333333;
  font-family:
    Helvetica Neue,
    Helvetica,
    sans-serif;
}

uni-page-body {
  height: 100%;

  &>uni-view {
    height: 100%;
  }
}

.shadow-warp {
  position: relative;
  box-shadow: 0 0 5px rgba(168, 92, 92, 0.1);
}

/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* #ifdef H5 */
.uni-tabbar{
  display: none;
}
/* #endif */
</style>
