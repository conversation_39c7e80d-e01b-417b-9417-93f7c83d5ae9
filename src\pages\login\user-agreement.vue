<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'default',
        navigationBarTitleText: '',
      },
    }
</route>

<template>
    <PageLayout :navbarShow="false" :isloginPage="true" :showFab="false">
        <view class="container">
            <view class="header">
                <text class="title">用户协议</text>
                <!-- <text class="date">生效日期：2024年1月1日</text> -->
            </view>

            <scroll-view class="content" scroll-y="true">
                <view class="section">
                    <text class="section-title">1. 协议简介</text>
                    <text class="section-content">
                        本用户协议是您与智能管理系统（以下简称"本系统"）之间的法律协议。使用本系统前，请您仔细阅读并充分理解本协议的全部内容。您的使用行为将被视为对本协议的接受和同意。
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">2. 服务内容</text>
                    <text class="section-content">
                        本系统为用户提供智能管理服务，包括但不限于：
                        • 用户信息管理
                        • 数据统计分析
                        • 系统功能使用
                        • 技术支持服务
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">3. 用户权利</text>
                    <text class="section-content">
                        用户在使用本系统时享有以下权利：
                        • 按照本协议约定使用本系统提供的服务
                        • 享受本系统提供的技术支持
                        • 在符合法律法规的前提下自由使用本系统
                        • 对本系统的服务质量进行监督和建议
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">4. 用户义务</text>
                    <text class="section-content">
                        用户在使用本系统时应当：
                        • 遵守相关法律法规和本协议的约定
                        • 不得利用本系统从事违法违规活动
                        • 保护自己的账号和密码安全
                        • 不得恶意攻击或破坏本系统
                        • 尊重他人的合法权益
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">5. 隐私保护</text>
                    <text class="section-content">
                        我们高度重视用户隐私保护：
                        • 严格按照隐私政策收集和使用用户信息
                        • 不会向第三方泄露用户个人信息
                        • 采用先进的技术手段保障信息安全
                        • 用户有权查询、更正和删除个人信息
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">6. 服务变更</text>
                    <text class="section-content">
                        本系统保留随时修改或中断服务的权利，包括但不限于：
                        • 系统功能的调整和优化
                        • 服务内容的增加或删除
                        • 技术架构的升级改造
                        • 因维护需要的临时中断
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">7. 免责声明</text>
                    <text class="section-content">
                        在适用法律允许的范围内，本系统不承担以下责任：
                        • 因网络故障、系统维护等原因导致的服务中断
                        • 因用户操作不当造成的损失
                        • 因第三方行为导致的问题
                        • 不可抗力因素造成的影响
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">8. 协议修改</text>
                    <text class="section-content">
                        本协议可能会根据业务发展需要进行修改，修改后的协议将在系统内公布。用户继续使用本系统即表示同意修改后的协议。
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">9. 联系我们</text>
                    <text class="section-content">
                        如您对本协议有任何疑问，请通过以下方式联系我们：
                        • 邮箱：xxx.com
                        • 电话：191xxxxxxxx
                        • 地址：广西壮族自治区南宁市西乡塘区神农大厦
                    </text>
                </view>
            </scroll-view>

            <view class="footer">
                <button class="agree-btn" @click="goBack">我已阅读并同意</button>
            </view>
        </view>
    </PageLayout>
</template>

<script>
export default {
    methods: {
        goBack() {
            uni.navigateBack();
        }
    }
}
</script>

<style scoped>
.container {
    min-height: 100vh;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
}

.header {
    background: #ffffff;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    text-align: center;
    display: block;
    margin-bottom: 5px;
}

.date {
    font-size: 12px;
    color: #666;
    text-align: center;
    display: block;
}

.content {
    flex: 1;
    padding: 20px;
}

.section {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 80%;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 15px;
    display: block;
}

.section-content {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    text-align: justify;
    white-space: pre-line;
}

.footer {
    background: #ffffff;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
}

.agree-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    color: #ffffff;
    border: none;
    padding: 6px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .header {
        padding: 15px;
    }

    .content {
        padding: 15px;
    }

    .section {
        padding: 15px;
    }

    .footer {
        padding: 15px;
    }
}
</style>