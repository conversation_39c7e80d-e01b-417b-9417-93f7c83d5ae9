<template>
    <wd-textarea :prop="prop" :name="name" :label="label" :label-width="labelWidth" v-model="textValue"
        :placeholder="placeholder" auto-height :disabled="disabled" />
    <soundTranform @addText="addText" :defaultData="defaultData" :name="name" :disabled="disabled"></soundTranform>

</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onUnload, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'
import { useUserStore } from '@/store'
import { getAudioTransform } from '@/service/index/foo'
import soundTranform from './soundTranform.vue'
const toast = useToast()
const textValue = ref('')
const addText = (e) => {
    textValue.value = textValue.value + e.data
    uni.$emit('setRemark', {
        param: textValue.value,
        name: props.name
    })
}

const userInfo = useUserStore().userInfo
const router = useRouter()
const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Array,
        default: []
    },
    defaultEditText: {
        type: String,
        default: ''
    },
    labelWidth: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

// #ifdef H5
onLoad(()=>{
    textValue.value = props.defaultEditText
})
// #endif

// #ifdef MP-WEIXIN
onReady(()=>{

    // textValue.value = props.defaultEditText

    // console.log(props.defaultEditText,textValue.value);
    
    setTimeout(() => {
        textValue.value = props.defaultEditText
        // toast.info(textValue.value)
    }, 50);

})
// #endif

watch(
    () => textValue.value,
    (n, o) => {
        console.log(n, o);
        uni.$emit('setRemark', {
            param: textValue.value,
            name: props.name
        })
    }
)

</script>

<style lang="scss" scoped></style>