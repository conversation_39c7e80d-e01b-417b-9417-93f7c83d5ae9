<route lang="json5" type="page">
  {
    style: {
      navigationStyle: 'custom',
      navigationBarTitleText: '',
    },
  }
</route>

<template>
  <PageLayout :navbarShow="false" :isloginPage="true" :showFab="false">
    <view class="login-container">
      <!-- 背景装饰 -->
      <view class="bg-wave"></view>

      <!-- 顶部Logo区域 -->
      <view class="header-section">
        <image class="logo" src="https://qn.gxcf.ltd/source/logo.png" mode="aspectFit"></image>
        <text class="system-title ml3" style="font-family: cursive;">营销管理系统</text>
      </view>

      <!-- 登录表单区域 -->
      <view class="form-container">
        <!-- 登录方式切换 -->
        <view class="login-tabs">
          <view class="tab-item" :class="{ active: loginType === 'account' }" @click="loginType = 'account'">
            <text class="tab-text">账号登录</text>
          </view>
          <view class="tab-item" :class="{ active: loginType === 'phone' }" @click="loginType = 'phone'">
            <text class="tab-text">一键登录</text>
          </view>
        </view>

        <!-- 账号密码登录 -->
        <view v-if="loginType === 'account'" class="form-section">
          <view class="input-group">
            <view class="input-item">
              <view class="input-icon">
                <text class="icon-user">👤</text>
              </view>
              <input class="input-field" type="number" placeholder="请输入账号" v-model="userName" />
            </view>
            <view class="input-item">
              <view class="input-icon">
                <text class="icon-lock">🔒</text>
              </view>
              <input class="input-field" type="text" :password="!showPassword" placeholder="请输入密码" v-model="password" />
              <view class="password-toggle z-999">
                <text class="toggle-icon" @click="togglePasswordVisibility">{{ showPassword ? '🙈' : '👁️' }}</text>
              </view>
            </view>
          </view>

          <view class="forgot-password">
            <text class="forgot-text" @click="forgotPassword">忘记密码？</text>
          </view>
        </view>

        <!-- 一键登录 -->
        <view v-if="loginType === 'phone'" class="form-section">
          <view class="one-click-login">
            <view class="one-click-icon">
              <text class="icon-mobile">📱</text>
            </view>
            <view class="one-click-content">
              <text class="one-click-title">一键登录</text>
              <text class="one-click-desc">使用本机号码验证，安全便捷</text>
            </view>
          </view>

          <!-- 一键登录按钮 -->
          <!-- <view class="one-click-btn" :class="{ disabled: !isAgreed }" @click="phoneLogin">
            <text class="one-click-text">本机号码一键登录</text>
          </view> -->
          <button class="one-click-btn" :class="{ disabled: !isAgreed }" open-type="getPhoneNumber"
            @getphonenumber="getPhoneNumber">
            <text class="one-click-text">本机号码一键登录</text>
          </button>

          <view class="login-tips">
            <view class="agreement-checkbox" @click="toggleAgreement">
              <view class="checkbox" :class="{ checked: isAgreed }">
                <text class="checkbox-icon" v-if="isAgreed">✓</text>
              </view>
              <view class="agreement-text">
                <text class="tips-text">我已阅读并同意</text>
                <text class="tips-link" @click.stop="openUserAgreement">《用户协议》</text>
                <text class="tips-text">和</text>
                <text class="tips-link" @click.stop="openPrivacyPolicy">《隐私政策》</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 登录按钮 -->
        <view v-if="loginType === 'account'" class="login-btn" @click="accountLogin">
          <text class="btn-text">{{ loading ? '登录...' : '登录' }}</text>
        </view>

        <!-- 底部链接 -->
        <!-- <view class="bottom-links">
                  <text class="link-text" @click="register">立即注册</text>
                  <text class="link-divider">|</text>
                  <text class="link-text" @click="customerService">客服支持</text>
              </view> -->
      </view>
    </view>
  </PageLayout>
</template>

<script lang="ts" setup>
import { useNotify, useToast } from 'wot-design-uni'
import { ref } from 'vue'
import { useUserStore } from '@/store/user'
import { http } from '@/utils/http'
import {
  HOME_PAGE,
} from '@/common/constants'
import { wechatLogin } from '@/service/menu/menu'
import { cache, getFileAccessHttpUrl } from '@/common/uitls'
import { useRouter } from '@/plugin/uni-mini-router'
import { useParamsStore } from '@/store/page-params'

import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
defineOptions({
  name: 'login',
  options: {
    // apply-shared‌：当前页面样式会影响到子组件样式.(小程序)
    // shared‌：当前页面样式影响到子组件，子组件样式也会影响到当前页面.(小程序)
    styleIsolation: 'shared',
  },
})
const router = useRouter()
const toast = useToast()
const userStore = useUserStore()

const loginType = ref('account')
const userName = ref()
const password = ref()
const showPassword = ref(false) //是否显示明文
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}
const loading = ref(false)
const isAgreed = ref(false) //同意协议
const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value
}

const paramsStore = useParamsStore()
paramsStore.reset()

if (import.meta.env.MODE === 'development') {
  userName.value = '052819'
  password.value = '123456'
}

if (import.meta.env.MODE === 'production') {
  userName.value = ''
  password.value = ''
}

const userInfo = useUserStore().userInfo

// 加载
onLoad((opts) => {
  console.log('login--------------', opts);
  // console.log(userInfo.token);
  // #ifdef H5
  if (userInfo.token) {
    // setTimeout(() => {
    //   router.replaceAll('/')
    // }, 50);
    router.replaceAll('/')
    return
  } else if (opts.username && opts.password) {
    userName.value = opts.username
    password.value = opts.password
    accountLogin()
  }
  // #endif
})

// 登录
const accountLogin = () => {
  if (userName.value.length === 0) {
    toast.warning('请输入账号')
    return
  }
  if (password.value.length === 0) {
    toast.warning('请输入密码')
    return
  }
  if (loading.value) { return }
  loading.value = true
  http
    .post('/sys/mLogin', { username: userName.value, password: password.value })
    .then((res: any) => {
      if (res.success) {
        const { result } = res
        const userInfo = result.userInfo
        userStore.setUserInfo({
          ...userInfo,
          token: result.token,
          userid: userInfo.id,
          username: userInfo.username,
          realname: userInfo.realname,
          avatar: userInfo.avatar,
          tenantId: userInfo.loginTenantId,
          localStorageTime: +new Date(),
        })
        console.log(userStore.userInfo);

        userStore.getZnkqId()
        router.pushTab({ path: HOME_PAGE })
      } else {
        toast.warning(res.message)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

const openUserAgreement = () => {
  uni.navigateTo({
    url: '/pages/login/user-agreement'
  });
}

const openPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/login/privacy-policy'
  });
}

// 手机一键登录
const phoneLogin = () => {

}

const getPhoneNumber = (e) => {
  console.log('getPhoneNumber', e);
  let {
    code,
    errMsg
  } = e.detail
  if (errMsg === 'getPhoneNumber:ok') {
    uni.login({
      success: async (res) => {
        console.log('wx.login', res);
        if (res.errMsg === 'login:ok') {
          let data = {
            mobileFastLoginCode: code,
            loginCode: res.code
          }
          console.log('请求信息', data);
          wechatLogin(data).then((res: any) => {

            console.log(res);
            if (res.success) {
              const { result } = res
              const userInfo = result.userInfo
              userStore.setUserInfo({
                ...userInfo,
                token: result.token,
                userid: userInfo.id,
                username: userInfo.username,
                realname: userInfo.realname,
                avatar: userInfo.avatar,
                tenantId: userInfo.loginTenantId,
                localStorageTime: +new Date(),
              })
              console.log(userStore.userInfo);

              userStore.getZnkqId()
              router.pushTab({ path: HOME_PAGE })
            } else {
              toast.warning(res.message)
            }

          })
          // this.$store.dispatch('wxLogin', data).then(() => {
          //   this.loginSuccess()
          // }).catch((e) => {
          //   uni.showToast({
          //     title: e.msg
          //   })
          //   // if (this.captchaEnabled) {

          //   // }
          // })
        } else {
          uni.showToast({
            title: res.errMsg
          })
        }
      },
      fail: (err) => {
        uni.showToast({
          title: err.message
        })
      }
    })
  } else {
    uni.showToast({
      title: errMsg
    })
  }
}

// 忘记密码
const forgotPassword = () => {
  uni.showModal({
    title: '提示',
    showCancel: false,
    content: '请联系后台人员电话:191XXXXXXXX',
    success: function (res) {
      if (res.confirm) {
      } else if (res.cancel) {
      }
    }
  });
}

</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  position: relative;
  overflow: hidden;
}

.bg-wave {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 0 50% 50%;
  transform: scaleX(1.5);
}

.header-section {
  padding: 100px 0 60px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.logo {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.system-title {
  font-size: 28px;
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-container {
  background: #ffffff;
  margin: 0 30px;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.login-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 30px;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
}

.tab-item.active {
  background: #4CAF50;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.tab-text {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: #ffffff;
}

.form-section {
  margin-bottom: 30px;
}

.input-group {
  margin-bottom: 20px;
}

.input-item {
  position: relative;
  margin-bottom: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.input-item:focus-within {
  border-color: #4CAF50;
  box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.1);
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  z-index: 1;
}

.input-field {
  width: 100%;
  height: 50px;
  padding: 0 15px 0 50px;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #333;
}

.input-field::placeholder {
  color: #999;
}

.code-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: #4CAF50;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.code-btn.disabled {
  background: #ccc;
  pointer-events: none;
}

.code-text {
  color: #ffffff;
  font-size: 12px;
}

.forgot-password {
  text-align: right;
  margin-top: 10px;
}

.forgot-text {
  color: #4CAF50;
  font-size: 14px;
}

.login-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2px);
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.btn-text {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
}

.bottom-links {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.link-text {
  color: #4CAF50;
  font-size: 14px;
  margin: 0 15px;
}

.link-divider {
  color: #ddd;
  font-size: 14px;
}

/* 一键登录样式 */
.one-click-login {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  margin-bottom: 20px;
}

.one-click-icon {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.icon-mobile {
  font-size: 24px;
  color: #ffffff;
}

.one-click-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.one-click-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.one-click-desc {
  font-size: 14px;
  color: #666;
}

.one-click-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
}

.one-click-btn:active {
  transform: translateY(2px);
  box-shadow: 0 4px 10px rgba(76, 175, 80, 0.3);
}

.one-click-btn.disabled {
  background: #e0e0e0;
  box-shadow: none;
  pointer-events: none;
}

.one-click-btn.disabled .one-click-text {
  color: #999;
}

.one-click-text {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.login-tips {
  text-align: center;
  margin-top: 20px;
  display: flex;
  align-items: center;
  // justify-content: center;
  flex-wrap: wrap;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 2px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox.checked {
  background: #4CAF50;
  border-color: #4CAF50;
}

.checkbox-icon {
  color: #ffffff;
  font-size: 10px;
  font-weight: bold;
}

.agreement-text {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;
}

.tips-text {
  font-size: 11px;
  color: #999;
  line-height: 1.5;
  margin: 0 2px;
}

.tips-link {
  font-size: 11px;
  color: #4CAF50;
  line-height: 1.5;
  text-decoration: underline;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .form-container {
    margin: 0 20px;
    padding: 30px 20px;
  }

  .system-title {
    font-size: 24px;
  }

  .logo {
    width: 100px;
    height: 100px;
  }

  .one-click-login {
    padding: 20px 0;
  }

  .one-click-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }

  .icon-mobile {
    font-size: 20px;
  }

  .one-click-title {
    font-size: 18px;
  }

  .login-tips {
    flex-direction: column;
    align-items: center;
  }

  .agreement-checkbox {
    flex-direction: column;
    align-items: center;
  }

  .checkbox {
    margin-right: 0;
    margin-bottom: 8px;
  }

  .agreement-text {
    flex-direction: column;
    align-items: center;
  }

  .tips-text,
  .tips-link {
    margin: 2px 0;
  }

  .password-toggle {
    width: 28px;
    height: 28px;
    right: 12px;
  }

  .toggle-icon {
    font-size: 14px;
  }
}

.password-input {
  padding-right: 50px;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  background: rgba(0, 0, 0, 0.05);
}

.toggle-icon {
  font-size: 16px;
  color: #666;
  transition: all 0.3s ease;
}

.password-toggle:active .toggle-icon {
  transform: scale(0.9);
}
</style>