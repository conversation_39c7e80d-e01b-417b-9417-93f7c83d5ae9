import { FormRules } from "wot-design-uni/components/wd-form/types"

const vt = {
    type111: 1, //拜访已合作经销商
    type222: 2, //拜访已合作零售店
    type333: 3, //开发【意向经销商】
    type444: 4, //开发【意向零售店】
    type555: 5, //召开三会
    type666: 6, //建立示范田（首次）
    type777: 7, //其他
}

const formConfig = ref([
    {
        title: '',
        form: [
            // 通用填写----------------------------------------------
            // 通用填写不设置showRules----------------------------------------------
            // 通用填写----------------------------------------------
            {
                type: 'dateTimePicker',
                label: '开始时间',
                name: 'startTime',
                pickerType: 'datetime',
                displayFormat: (items) => {
                    return `${items[0].label}年${items[1].label}月${items[2].label}日 ${items[3].label}:${items[4].label}`
                }
            },
            {
                type: 'text',
                label: '停留时长',
                name: 'stayTime'
            },
            {
                type: 'dateTimePicker',
                label: '结束时间',
                name: 'endTime',
                pickerType: 'datetime',
                displayFormat: (items) => {
                    return `${items[0].label}年${items[1].label}月${items[2].label}日 ${items[3].label}:${items[4].label}`
                }
            },
            {
                type: 'selectLocationByMap',
                label: '地址',
                name: 'address',
                placeholder: '请输入地址',
                showCopy: false,
            },
            {
                type: 'picker',
                label: '拜访类型',
                name: 'visitType',
                columns: [
                    {
                        value: 1,
                        label: '拜访已合作经销商'
                    },
                    {
                        value: 2,
                        label: '拜访已合作零售店'
                    },
                    {
                        value: 3,
                        label: '开发【意向经销商】'
                    },
                    {
                        value: 4,
                        label: '开发【意向零售店】'
                    },
                    {
                        value: 5,
                        label: '召开三会'
                    },
                    {
                        value: 6,
                        label: '建立示范田'
                    },
                    {
                        value: 7,
                        label: '其他'
                    }
                ],
                placeholder: '请选择拜访类型'
            },
            {
                type: 'selectPopup',
                label: '选择经销商',
                name: 'JXS',
                formData: {},
                showRules: {
                    by: 'visitType',
                    arr: [vt.type111, vt.type222, vt.type333, vt.type444, vt.type555, vt.type666]
                }
            },
            {
                type: 'selectPopup',
                label: '选择零售店',
                name: 'LSD',
                formData: {},
                showRules: {
                    by: 'visitType',
                    arr: [vt.type222, vt.type444]
                }
            },
            {
                type: 'checkbox',
                label: '具体动作',
                name: 'jutidongzuo',
                groupData: [{
                    value: 1,
                    label: '驻点促销'
                }, {
                    value: 2,
                    label: '店面亮化'
                }, {
                    value: 3,
                    label: '常规拜访'
                }],
                showRules: {
                    by: 'visitType',
                    arr: [vt.type222, vt.type444]
                }
            },
            {
                type: 'checkbox',
                label: '会议类型',
                name: 'zhaokaisanhui',
                groupData: [{
                    value: 1,
                    label: '经销商会'
                }, {
                    value: 2,
                    label: '促销会'
                }, {
                    value: 3,
                    label: '观摩会'
                }],
                showRules: {
                    by: 'visitType',
                    arr: [vt.type555]
                }
            },
            {
                type: 'radio',
                label: '',
                name: 'shifantian',
                groupData: [{
                    value: 1,
                    label: '首次建立示范田'
                }, {
                    value: 2,
                    label: '回访示范田'
                }],
                showRules: {
                    by: 'visitType',
                    arr: [vt.type666]
                }
            },
            {
                type: 'upload',
                label: '拜访过程水印照片',
                name: 'origin_clientGatePics',
                formData: {},
                showRules: {
                    by: 'visitType',
                    arr: [vt.type111, vt.type222, vt.type333, vt.type444, vt.type666, vt.type777]
                }
            },
            {
                type: 'textarea',
                label: '拜访成果',
                name: 'interviewDescription',
                placeholder: '请输入拜访成果',
                showRules: {
                    by: 'visitType',
                    arr: [vt.type111, vt.type222, vt.type333, vt.type444, vt.type666, vt.type777]
                }
            },
            {
                type: 'upload',
                label: '会议水印照片',
                name: 'origin_clientGatePics',
                formData: {},
                showRules: {
                    by: 'visitType',
                    arr: [vt.type555]
                }
            },
            {
                type: 'textarea',
                label: '会议成果',
                name: 'interviewDescription',
                placeholder: '请输入会议成果',
                showRules: {
                    by: 'visitType',
                    arr: [vt.type555]
                }
            },
            // 客户拜访------------------------------------------------------------------------
            // 首次。重复。回访。（包括订货会、观摩会展示）------------------------------------------------------------------------
            // 客户拜访------------------------------------------------------------------------
            // {
            //     type: 'selectClientInfo', //选择的客户信息---------------------
            //     name: 'custom_selectClientInfoDefaultData',
            //     label: '客户信息',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.repeatVisit, vt.clientReview]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '客户姓名',
            //     name: 'clientName',
            //     placeholder: '请输入客户姓名或昵称',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.orderMeeting, vt.obserMeeting, vt.salesAchievements, vt.productPromotion, vt.farmerMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '客户联系方式',
            //     name: 'clientPhone',
            //     placeholder: '请输入手机号或者微信',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.orderMeeting, vt.obserMeeting, vt.salesAchievements, vt.productPromotion, vt.farmerMeeting]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '客户门店截图或其他照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.repeatVisit, vt.clientReview, vt.orderMeeting, vt.obserMeeting]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '客户微信截图',
            //     formData: {},
            //     name: 'origin_wechatScreenshotPics',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.repeatVisit, vt.clientReview, vt.orderMeeting]
            //     }
            // },
            // {
            //     type: 'textareawithsound',
            //     label: '拜访描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入拜访描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.repeatVisit, vt.clientReview, vt.orderMeeting]
            //     }
            // },
            // {
            //     type: 'textareawithsound',
            //     label: '拜访描述2',
            //     name: 'interviewDescription2',
            //     placeholder: '请输入拜访描述2',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.firstVisit, vt.repeatVisit, vt.clientReview, vt.orderMeeting, vt.obserMeeting]
            //     }
            // },
            // 生测界面--------------------------------------------------------------------------
            // 生测界面--------------------------------------------------------------------------
            // 生测界面--------------------------------------------------------------------------
            // {
            //     type: 'picker',
            //     label: '生测实验操作次数',
            //     name: 'modelNum',
            //     defaultText: '请选择操作次数',
            //     columns: [1, 2, 3, 4, 5],
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.bioTest]
            //     }
            // },
            //modelNum---次数---首测生测实验需要填写客户信息
            // {
            //     type: 'input',
            //     label: '农户姓名或昵称',
            //     name: 'clientName',
            //     placeholder: '请输入农户姓名或昵称',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [1],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.bioTest]
            //         }
            //     ]
            // },
            // {
            //     type: 'input',
            //     label: '农户联系方式',
            //     name: 'clientPhone',
            //     placeholder: '请输入农户联系方式',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [1],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.bioTest]
            //         }
            //     ]
            // },
            // {
            //     type: 'selectClientInfo',
            //     name: 'custom_selectClientInfoDefaultData',
            //     label: '农户信息',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [2, 3, 4, 5],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.bioTest]
            //         }
            //     ]
            // },
            // {
            //     type: 'picker',
            //     label: '本次操作',
            //     name: 'thisWorkType',
            //     columns: [],
            //     defaultText: '请选择本次操作类型',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.bioTest]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '生测实验工作视频',
            //     name: 'origin_clientVideo',
            //     accept: 'video',
            //     widthProp: '500',
            //     heightProp: '250',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.bioTest]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '生测实验照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.bioTest]
            //     }
            // },
            // {
            //     // type: 'textarea',
            //     type: 'textareawithsound',
            //     label: '生测实验工作描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入生测实验工作描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.bioTest]
            //     }
            // },
            // // 示范田界面-----------------------------------------------------------------------------------
            // // 示范田界面-----------------------------------------------------------------------------------
            // // 示范田界面-----------------------------------------------------------------------------------
            // {
            //     type: 'picker',
            //     label: '示范田操作次数',
            //     name: 'modelNum',
            //     defaultText: '请选择操作次数',
            //     columns: [1, 2, 3, 4, 5],
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.modelField]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '农户姓名或昵称',
            //     name: 'clientName',
            //     placeholder: '请输入农户姓名或昵称',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [1],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.modelField]
            //         }
            //     ]
            // },
            // {
            //     type: 'input',
            //     label: '农户联系方式',
            //     name: 'clientPhone',
            //     placeholder: '请输入农户联系方式',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [1],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.modelField]
            //         }
            //     ]
            // },
            // {
            //     type: 'selectClientInfo',
            //     name: 'custom_selectClientInfoDefaultData',
            //     label: '农户信息',
            //     showRules: [
            //         {
            //             by: 'modelNum',
            //             arr: [2, 3, 4, 5],
            //         },
            //         {
            //             by: 'visitType',
            //             arr: [vt.modelField]
            //         }
            //     ]
            // },
            // {
            //     type: 'picker',
            //     label: '本次操作',
            //     name: 'thisWorkType',
            //     columns: [],
            //     defaultText: '请选择本次操作类型',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.modelField]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '示范田工作视频',
            //     name: 'origin_clientVideo',
            //     accept: 'video',
            //     widthProp: '500',
            //     heightProp: '250',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.modelField]
            //     }
            // },
            // {
            //     type: 'upload',
            //     label: '示范田照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.modelField]
            //     }
            // },
            // {
            //     // type: 'textarea',
            //     type: 'textareawithsound',
            //     label: '示范田工作描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入示范田工作描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.modelField]
            //     }
            // },
            // // 订货会页面------------------------------------------------------------------

            // // 观摩会页面------------------------------------------------------------------
            // {
            //     type: 'input',
            //     label: '观摩产品',
            //     name: 'custom_productName',
            //     placeholder: '请输入观摩产品',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.obserMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '销售数量',
            //     textType: 'number',
            //     name: 'custom_productNum',
            //     placeholder: '请输入销售数量',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.obserMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '参会人数',
            //     textType: 'number',
            //     name: 'custom_joinPersonNum',
            //     placeholder: '请输入参会人数',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.obserMeeting]
            //     }
            // },
            // {
            //     type: 'textareawithsound',
            //     label: '观摩会描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入观摩会描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.obserMeeting]
            //     }
            // },
            // // 工作笔记记录页面------------------------------------------------------------------
            // // 工作笔记记录页面------------------------------------------------------------------
            // // 工作笔记记录页面------------------------------------------------------------------
            // {
            //     type: 'upload',
            //     label: '工作笔记记录截图或相关照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.workRecord]
            //     }
            // },
            // {
            //     // type: 'textarea',
            //     type: 'textareawithsound',
            //     label: '工作笔记记录工作描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入工作笔记记录工作描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.workRecord]
            //     }
            // },
            // // 拉练页面---------------------------------------------------------------------------
            // // 拉练页面---------------------------------------------------------------------------
            // // 拉练页面---------------------------------------------------------------------------
            // {
            //     type: 'upload',
            //     label: '拉练相关照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     // type: 'textarea',
            //     type: 'textareawithsound',
            //     label: '拉练工作描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入拉练描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '拉条幅',
            //     textType: 'number',
            //     name: 'bannerNum',
            //     useSuffix: true,
            //     suffixText: '元',
            //     placeholder: '请输入',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '贴宣传画',
            //     textType: 'number',
            //     name: 'picturePosterNum',
            //     placeholder: '请输入',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '发单页',
            //     textType: 'number',
            //     name: 'leafletNum',
            //     placeholder: '请输入',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '站门店销售额',
            //     textType: 'number',
            //     name: 'storeSalesVolume',
            //     placeholder: '请输入',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'textarea',
            //     label: '配药',
            //     name: 'dosageDesc',
            //     placeholder: '配药',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'textarea',
            //     label: '打药',
            //     name: 'sprayDesc',
            //     placeholder: '打药',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'textarea',
            //     label: '巡田',
            //     name: 'patrolDesc',
            //     placeholder: '巡田',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // {
            //     type: 'textarea',
            //     label: '出方案',
            //     name: 'issuedPlanDesc',
            //     placeholder: '出方案',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.camp]
            //     }
            // },
            // // 站点促销页面---------------------------------------------------------------------------
            // // 站点促销页面---------------------------------------------------------------------------
            // // 站点促销页面---------------------------------------------------------------------------
            // {
            //     type: 'upload',
            //     label: '促销成果截图或相关照片',
            //     name: 'origin_clientGatePics',
            //     formData: {},
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.salesAchievements]
            //     }
            // },
            // {
            //     type: 'visitSalesAchievements',
            //     name: 'visitSalesAchievements',
            //     label: '销售战绩',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.salesAchievements]
            //     }
            // },
            // {
            //     type: 'textareawithsound',
            //     label: '促销工作总结',
            //     name: 'interviewDescription',
            //     placeholder: '请输入促销工作总结',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.salesAchievements]
            //     }
            // },
            // // 产品宣传页面---------------------------------------------------------------------------
            // // 产品宣传页面---------------------------------------------------------------------------
            // // 产品宣传页面---------------------------------------------------------------------------
            // {
            //     type: 'input',
            //     label: '产品名称',
            //     name: 'custom_productName',
            //     placeholder: '请输入产品名称',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.productPromotion]
            //     }
            // },
            // {
            //     type: 'picker',
            //     label: '工作内容',
            //     name: 'custom_workContent',
            //     columns: [],
            //     placeholder: '请选择工作内容',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.productPromotion]
            //     }
            // },
            // // 培训页面---------------------------------------------------------------------------
            // // 培训页面---------------------------------------------------------------------------
            // // 培训页面---------------------------------------------------------------------------
            // {
            //     type: 'picker',
            //     label: '培训形式',
            //     name: 'custom_trainType',
            //     columns: [],
            //     placeholder: '请选择培训形式',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.peixun]
            //     }
            // },
            // {
            //     type: 'textarea',
            //     label: '培训主题',
            //     name: 'custom_trainTheme',
            //     placeholder: '请输入培训主题',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.peixun]
            //     }
            // },
            // // 例会页面---------------------------------------------------------------------------
            // // 例会页面---------------------------------------------------------------------------
            // // 例会页面---------------------------------------------------------------------------
            // {
            //     type: 'picker',
            //     label: '例会类型',
            //     name: 'custom_regularMeetingType',
            //     columns: [],
            //     placeholder: '请选择例会类型',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '主持人',
            //     name: 'custom_regularMeetingHost',
            //     placeholder: '请输入主持人',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '参会人',
            //     name: 'custom_regularMeetingJoin',
            //     placeholder: '请输入参会人',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '应到人数',
            //     name: 'custom_regularMeetingReachNum',
            //     textType: 'number',
            //     placeholder: '请输入应到人数',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '请假人数',
            //     name: 'custom_regularMeetingLeaveNum',
            //     textType: 'number',
            //     placeholder: '请输入请假人数',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // // {
            // //     custom_regularMeetingTime
            // // }
            // {
            //     type: 'textarea',
            //     label: '会议主题',
            //     name: 'custom_regularMeetingContent',
            //     placeholder: '请输入会议主题',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.regularMeeting]
            //     }
            // },
            // // 农民会页面---------------------------------------------------------------------------
            // // 农民会页面---------------------------------------------------------------------------
            // // 农民会页面---------------------------------------------------------------------------
            // {
            //     type: 'input',
            //     label: '销售产品名称',
            //     name: 'custom_productName',
            //     placeholder: '请输入销售产品名称',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.farmerMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '销售数量',
            //     name: 'custom_productNum',
            //     textType: 'number',
            //     placeholder: '请输入销售数量',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.farmerMeeting]
            //     }
            // },
            // {
            //     type: 'input',
            //     label: '参会人数',
            //     name: 'custom_joinPersonNum',
            //     textType: 'number',
            //     placeholder: '请输入参会人数',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.farmerMeeting]
            //     }
            // },
            // {
            //     type: 'textareawithsound',
            //     label: '农民会描述',
            //     name: 'interviewDescription',
            //     placeholder: '请输入农民会描述',
            //     showRules: {
            //         by: 'visitType',
            //         arr: [vt.farmerMeeting]
            //     }
            // },
            // // 公用同行人------------------------------------------------------------
            // // 公用同行人------------------------------------------------------------
            // // 公用同行人------------------------------------------------------------
            // {
            //     type: 'selectPartners',
            //     name: 'selectPartners',
            //     label: '同行人',
            // },
        ]
    }
])

// rules======================================================================
// rules======================================================================
// rules======================================================================
// rules======================================================================
// rules======================================================================
// rules======================================================================
// rules======================================================================

const rules: FormRules = {
    // // 我的地址
    // address: [
    //     {
    //         required: true,
    //         message: '请填写地址',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 拜访类型
    // visitType: [
    //     {
    //         required: true,
    //         message: '请填写拜访类型',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 客户地址
    // clientGateAddress: [
    //     {
    //         required: true,
    //         message: '请选择拜访地址',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 客户
    // clientName: [
    //     {
    //         required: true,
    //         message: '请填客户姓名或昵称',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 客户手机号联系方式
    // clientPhone: [
    //     {
    //         required: true,
    //         message: '请填写客户联系方式',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 选择客户信息
    // custom_selectClientInfoDefaultData: [
    //     {
    //         required: true,
    //         validator: (value) => {
    //             if (!value) {
    //                 return false
    //             } else if (typeof value == 'object' && Object.keys(value).length !== 0) {
    //                 return true
    //             } else {
    //                 return true;
    //             }
    //         },
    //         message: '请选择客户信息',
    //         trigger: ['change', 'blur'],
    //     }
    // ],
    // // 照片
    // origin_clientGatePics: [
    //     {
    //         // required: true,
    //         required: false,
    //         message: '请上传照片',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 微信照片
    // origin_wechatScreenshotPics: [
    //     {
    //         // required: true,
    //         required: false,
    //         message: '请上传照片',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 视频
    // origin_clientVideo: [
    //     {
    //         // required: true,
    //         required: false,
    //         message: '请上传视频',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 生测、示范田操作次数
    // modelNum: [
    //     {
    //         required: true,
    //         message: '请选择操作次数',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 生测、示范田工作类型
    // thisWorkType: [
    //     {
    //         required: true,
    //         message: '请选择本次操作',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 产品名称
    // custom_productName: [
    //     {
    //         required: true,
    //         message: '请填写产品名称',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 销售数量
    // custom_productNum: [
    //     {
    //         required: true,
    //         message: '请填写销售数量',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 参会人数
    // custom_joinPersonNum: [
    //     {
    //         required: true,
    //         message: '请填写参会人数',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 工作内容
    // custom_workContent: [
    //     {
    //         required: true,
    //         message: '请选择工作内容',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 培训形式
    // custom_trainType: [
    //     {
    //         required: true,
    //         message: '请填写培训形式',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 培训主题
    // custom_trainTheme: [
    //     {
    //         required: true,
    //         message: '请选择培训主题',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 例会类型
    // custom_regularMeetingType: [
    //     {
    //         required: true,
    //         message: '请选择例会类型',
    //         trigger: ['blur', 'change']
    //     }
    // ],
    // // 主持人
    // custom_regularMeetingHost: [{
    //     required: true,
    //     message: '请填写主持人',
    //     trigger: ['blur', 'change']
    // }],
    // // 参会人
    // custom_regularMeetingJoin: [{
    //     required: true,
    //     message: '请填写参会人',
    //     trigger: ['blur', 'change']
    // }],
    // // 应到人数
    // custom_regularMeetingReachNum: [{
    //     required: true,
    //     message: '请填写应到人数',
    //     trigger: ['blur', 'change']
    // }],
    // // 请假人数
    // custom_regularMeetingLeaveNum: [{
    //     required: true,
    //     message: '请填写请假人数',
    //     trigger: ['blur', 'change']
    // }],
    // // 会议主题
    // custom_regularMeetingContent: [{
    //     required: true,
    //     message: '请填写会议主题',
    //     trigger: ['blur', 'change']
    // }],
}

export { formConfig, rules };