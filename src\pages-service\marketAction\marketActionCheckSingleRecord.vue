<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '拜访客户记录',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout navTitle="拜访客户记录" :showFab="false">
        <!-- <template #navRight>
            <wd-icon name="share" size="30px" color="#ffcccc" @click="showShareSomeone" v-if="showShareByPerm" />
        </template> -->
        <scroll-view scroll-y>
            <!-- 默认展示 -->
            <view class="ml1 mr1 mt1 p1 b-rd-10">
                <wd-cell-group border>
                    <wd-cell title="拜访类别" :value="recordDetail.visitType_dictText" />
                    <wd-cell title="开始时间" :value="recordDetail.startTime" />
                    <wd-cell title="结束时间" :value="recordDetail.endTime" />
                    <wd-cell title="停留时长"
                        :value="gethms(new Date(recordDetail.endTime).getTime() - new Date(recordDetail.startTime).getTime())" />
                    <wd-cell title="地址" :value="recordDetail.address" />
                </wd-cell-group>
            </view>

            <!-- 1首次拜访--------------------------------------------------- -->
            <!-- 2未成交重复拜访--------------------------------------------------- -->
            <!-- 3已成交客户回访--------------------------------------------------- -->
            <!-- 6订货会--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.firstVisit ||
                recordVisitType == vt.repeatVisit ||
                recordVisitType == vt.clientReview ||
                recordVisitType == vt.orderMeeting" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="客户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="客户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="客户/拜访时位置" :value="recordDetail.clientGateAddress" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">客户门店截图或其它照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">客户微信页截图</view>
                    <wd-img v-for="(item, index) in recordDetail.wechatScreenshotPics" :width="imgWidth"
                        :height="imgHeight" custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">拜访描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 4生测实验--------------------------------------------------- -->
            <!-- 4生测实验--------------------------------------------------- -->
            <!-- 4生测实验--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.bioTest" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="实验操作次数" :value="recordDetail.modelNum" />
                    <wd-cell title="农户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="农户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="生测实验位置" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="本次操作" :value="recordDetail.thisWorkType_dictText" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">生测实验视频</view>
                    <wd-swiper :list="recordDetail.clientVideo" value-key="url" autoplay stopAutoplayWhenVideoPlay
                        :autoplayVideo="false" :indicator="{ type: 'fraction' }"
                        indicator-position="top-right"></wd-swiper>
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">生测实验照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">生测实验描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 5示范田工作--------------------------------------------------- -->
            <!-- 5示范田工作--------------------------------------------------- -->
            <!-- 5示范田工作--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.modelField" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="示范田操作次数" :value="recordDetail.modelNum" />
                    <wd-cell title="农户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="农户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="示范田位置" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="本次操作" :value="recordDetail.thisWorkType_dictText" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">示范田工作视频</view>
                    <wd-swiper :list="recordDetail.clientVideo" value-key="url" autoplay stopAutoplayWhenVideoPlay
                        :autoplayVideo="false" :indicator="{ type: 'fraction' }"
                        indicator-position="top-right"></wd-swiper>
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">示范田工作照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">示范田工作描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 7观摩会--------------------------------------------------- -->
            <!-- 7观摩会--------------------------------------------------- -->
            <!-- 7观摩会--------------------------------------------------- -->
            <!-- 7观摩会--------------------------------------------------- -->
            <!-- 7观摩会--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.obserMeeting" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="客户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="客户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="观摩会位置" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="观摩产品" :value="recordDetail?.visitTypeInfo?.productName" />
                    <wd-cell title="销售数量" :value="recordDetail?.visitTypeInfo?.productNum" />
                    <wd-cell title="参会人数" :value="recordDetail?.visitTypeInfo?.joinPersonNum" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">客户门店截图或其它照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">观摩会工作描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 8工作笔记记录--------------------------------------------------- -->
            <!-- 8工作笔记记录--------------------------------------------------- -->
            <!-- 8工作笔记记录--------------------------------------------------- -->
            <!-- 8工作笔记记录--------------------------------------------------- -->
            <!-- 8工作笔记记录--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.workRecord" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="工作位置" :value="recordDetail.clientGateAddress" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">工作笔记记录截图或相关照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">工作笔记记录描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 9拉练--------------------------------------------------- -->
            <!-- 9拉练--------------------------------------------------- -->
            <!-- 9拉练--------------------------------------------------- -->
            <!-- 9拉练--------------------------------------------------- -->
            <!-- 9拉练--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.camp" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="拉练位置" :value="recordDetail.clientGateAddress" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">拉练相关照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">拉练工作描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <wd-cell-group border>
                    <wd-cell title="拉条幅" :value="recordDetail.bannerNum" />
                    <wd-cell title="贴宣传画" :value="recordDetail.picturePosterNum" />
                    <wd-cell title="发页单" :value="recordDetail.leafletNum" />
                    <wd-cell title="站门店销售额" :value="recordDetail.storeSalesVolume" />
                    <wd-cell title="配药" :value="recordDetail.dosageDesc" />
                    <wd-cell title="打药" :value="recordDetail.sprayDesc" />
                    <wd-cell title="巡田" :value="recordDetail.patrolDesc" />
                    <wd-cell title="出方案" :value="recordDetail.issuedPlanDesc" />
                </wd-cell-group>
            </view>
            <!-- 10站点促销--------------------------------------------------- -->
            <!-- 10站点促销--------------------------------------------------- -->
            <!-- 10站点促销--------------------------------------------------- -->
            <!-- 10站点促销--------------------------------------------------- -->
            <!-- 10站点促销--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.salesAchievements" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="客户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="客户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="客户/拜访时位置" :value="recordDetail.clientGateAddress" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">门店截图或其它照片</view>
                    <wd-img v-for="(item, index) in recordDetail.clientGatePics" :width="imgWidth" :height="imgHeight"
                        custom-class="m1" :src="item.url" :enable-preview="true" />
                </view>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <wd-table :data="recordDetail?.visitSalesAchievements || []" :height="400" custom-class="p2">
                    <wd-table-col prop="productName" label="商品名" :width="180"></wd-table-col>
                    <wd-table-col prop="productNum" label="数量" :width="70"></wd-table-col>
                    <wd-table-col prop="productUnit_dictText" label="单位" :width="70"></wd-table-col>
                </wd-table>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">促销工作总结</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 11产品宣传--------------------------------------------------- -->
            <!-- 11产品宣传--------------------------------------------------- -->
            <!-- 11产品宣传--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.productPromotion" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="客户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="客户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="客户/拜访时位置" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="产品名称" :value="recordDetail?.visitTypeInfo?.productName" />
                    <wd-cell title="工作内容" :value="recordDetail?.visitTypeInfo?.workContent_dictText" />
                </wd-cell-group>
            </view>
            <!-- 12培训--------------------------------------------------- -->
            <!-- 12培训--------------------------------------------------- -->
            <!-- 12培训--------------------------------------------------- -->
            <!-- 12培训--------------------------------------------------- -->
            <!-- 12培训--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.peixun" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="培训地点" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="培训形式" :value="recordDetail?.visitTypeInfo?.trainType_dictText" />
                    <wd-cell title="培训主题" :value="recordDetail?.visitTypeInfo?.trainTheme" />
                </wd-cell-group>
            </view>
            <!-- 13例会--------------------------------------------------- -->
            <!-- 13例会--------------------------------------------------- -->
            <!-- 13例会--------------------------------------------------- -->
            <!-- 13例会--------------------------------------------------- -->
            <!-- 13例会--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.regularMeeting" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="例会类型" :value="recordDetail?.visitTypeInfo?.regularMeetingType_dictText" />
                    <wd-cell title="主持人" :value="recordDetail?.visitTypeInfo?.regularMeetingHost" />
                    <wd-cell title="参会人" :value="recordDetail?.visitTypeInfo?.regularMeetingJoin" />
                    <wd-cell title="应到人数" :value="recordDetail?.visitTypeInfo?.regularMeetingReachNum" />
                    <wd-cell title="请假人数" :value="recordDetail?.visitTypeInfo?.regularMeetingLeaveNum" />
                    <wd-cell title="例会主题" :value="recordDetail?.visitTypeInfo?.regularMeetingContent" />
                </wd-cell-group>
            </view>
            <!-- 14农民会--------------------------------------------------- -->
            <!-- 14农民会--------------------------------------------------- -->
            <!-- 14农民会--------------------------------------------------- -->
            <!-- 14农民会--------------------------------------------------- -->
            <!-- 14农民会--------------------------------------------------- -->
            <view v-if="recordVisitType == vt.farmerMeeting" class="ml2 mr2 mt1 p2 b-rd-0 bg-#ffffff">
                <wd-cell-group border>
                    <wd-cell title="客户姓名或昵称" :value="recordDetail.clientName" />
                    <wd-cell title="客户联系方式" :value="recordDetail.clientPhone" />
                    <wd-cell title="农民会位置" :value="recordDetail.clientGateAddress" />
                    <wd-cell title="销售产品名称" :value="recordDetail?.visitTypeInfo?.productName" />
                    <wd-cell title="销售数量" :value="recordDetail?.visitTypeInfo?.productNum" />
                    <wd-cell title="参会人数" :value="recordDetail?.visitTypeInfo?.joinPersonNum" />
                </wd-cell-group>
                <wd-divider>
                    <wd-icon name="circle" size="10" color="#1989fa" />
                </wd-divider>
                <view class="m2">
                    <view class="mt1 mb1 font-size-4">农民会工作描述</view>
                    <view>{{ recordDetail.interviewDescription }}</view>
                    <view class="flex flex-wrap mt2">
                        <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudio(item)">
                            <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                                <view class="">{{ item.duration }}秒</view>
                                <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                                    src="/static/index/audioplay.png" />
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <!-- 默认展示 同行人分享人 ---------------------------------------->
            <!-- 默认展示 同行人分享人 ---------------------------------------->
            <!-- 默认展示 同行人分享人 ---------------------------------------->
            <view class="m2 mb3 p2 b-rd-0 bg-#ffffff">
                <view class="flex">
                    <view class="w50% flex flex-col flex-items-center font-size-3">
                        <view class="font-size-4">同行人</view>
                        <view v-for="item in visitPartners" :key="item.index">
                            {{ `${item.userName} - ${item.userAccount}` }}
                        </view>
                    </view>
                    <view class="w50% flex flex-col flex-items-center font-size-3">
                        <view class="flex items-center font-size-4">
                            <wd-icon name="share" size="20px" color="#ffcccc" @click="showShareSomeone"
                                v-if="showShareByPerm" />
                            <view>分享人</view>
                        </view>
                        <view v-for="item in visitShares" :key="item.index">
                            {{ `${item.userName} - ${item.userAccount}` }}
                            <wd-icon name="delete" size="1rem" color="red" @click="delShare(item)"
                                v-if="showShareDelByPerm"></wd-icon>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 审核展示 ---------------------------------------------------->
            <!-- 审核展示 ---------------------------------------------------->
            <!-- 审核展示 ---------------------------------------------------->
            <view class="m2 mb3 p2 b-rd-0 bg-#ffffff" v-if="recordDetail.auditStatus != 0">
                <view class="flex items-center font-size-4 mb2">
                    <view class="">审核人：</view>
                    <view class="ml4 color-black">{{ recordDetail.auditUserName }}</view>
                </view>
                <view class="flex items-center font-size-4 mb2">
                    <view class="">审核时间：</view>
                    <view class="ml4 color-black">{{ recordDetail.auditTime }}</view>
                </view>
                <view class="mb2 font-size-4">审核意见：
                    <wd-tag type="success" round v-if="recordDetail.auditStatus == 1">人工审核通过</wd-tag>
                    <wd-tag type="success" round v-if="recordDetail.auditStatus == 2">系统审核通过</wd-tag>
                    <wd-tag type="danger" round v-if="recordDetail.auditStatus == 3">人工审核不通过</wd-tag>
                </view>
                <view class="mb1  font-size-4" v-if="recordDetail.auditStatus == 1 || recordDetail.auditStatus == 3">{{
                    recordDetail.auditOpinion }}</view>
            </view>

            <view class="flex  flex-wrap items-center pb4">
                <!-- <view class="w45% ml2 mt2"> -->
                <!-- </view> -->
                <!-- <view class="w45% ml2 mt2"> -->
                <wd-button custom-class="w50% mb2" type="primary" size="large" @click="toEdit"
                    v-if="showEditRecord">修改</wd-button>
                <wd-button custom-class="w50% mb2" type="warning" size="large" @click="toFinalSubmit"
                    v-if="showFinalSubmit">最终提交</wd-button>
                <wd-button custom-class="w50% mb2" type="error" size="large" @click="toDel"
                    v-if="showDeleteRecord">删除</wd-button>
                <!-- </view> -->
                <!-- <view class="w45% ml2 mt2"> -->
                <wd-button custom-class="w100%" type="warning" size="large" @click="toValid"
                    v-if="showValidRecord">审核</wd-button>
                <!-- </view> -->
            </view>



            <!-- <wd-input-number v-model="recordVisitType" /> -->
        </scroll-view>
    </PageLayout>
    <wd-popup v-model="showShare" custom-style="h30" @close="closeShare" position="bottom">
        <wd-search v-model="searchShareValue" @search="searchShare" placeholder="请输入姓名搜索" hide-cancel
            @clear="searchShare" />
        <wd-picker-view :columns="shareColumns" v-model="shareValue" @change="onChangeShare" custom-class="" />
        <wd-button custom-class="w100%" type="primary" @click="handleShareOne">分享</wd-button>
    </wd-popup>

    <wd-message-box selector="validBox">
        <wd-textarea v-model="validText" placeholder="请填写审核意见" />
        <wd-divider></wd-divider>
        <wd-radio-group v-model="validPass" inline shape="dot" checked-color="#4D80F0" size="large">
            <wd-radio value="3">不通过</wd-radio>
            <wd-radio value="1">通过</wd-radio>
        </wd-radio-group>
    </wd-message-box>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import {
    getVisitRecordById,
    getMyShareList,
    getMyPartnerList,
    getUserTenantPageList,
    addSharePeople,
    delSharePeople,
    delMarketActionRecord,
    auditVisit,
    editMarketActionRecord,
    finalSubmission,
    getVisitSalesAchievement,
    getVisitTypeInfoById
} from '@/service/marketAction/marketAction';
import { hasPerms } from '@/utils';
import { decrypt } from '@/utils/crypto';

import { encrypt } from '@/utils/crypto'

import { useToast, useMessage } from 'wot-design-uni'
const toast = useToast()
const message = useMessage()
const validBox = useMessage('validBox')
const validPass = ref(-1)
const validText = ref('')

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useGlobalStore } from '@/store';
const global = useGlobalStore().global
const menus = useGlobalStore().global.menus

const imgWidth = ref(73)
const imgHeight = ref(73)
const recordVisitType = ref(0)
const vt = {
    firstVisit: 1, //首次拜访
    repeatVisit: 2, //未成交重复拜访
    clientReview: 3, //已成交客户回访
    bioTest: 4, //生测实验
    modelField: 5, //示范田工作
    orderMeeting: 6, //订货会
    obserMeeting: 7, //观摩会
    workRecord: 8, //工作笔记记录
    camp: 9, //拉练
    salesAchievements: 10,//站点促销
    productPromotion: 11,//产品宣传
    peixun: 12,//培训
    regularMeeting: 13,//例会
    farmerMeeting: 14,//农民会
}
const recordId = ref('')
const recordDetail = ref<any>({})
// 同行人列表
const visitPartners = ref([])
// 分享人列表
const visitShares = ref([])


// 权限
const permissions = ref({
    edit: true,
    delete: true,
    valid: false,
    share: true,
    finalSubmit: true
})
// 删除
const showDeleteRecord = computed(() => {
    return hasPerms(menus, 'action:t_visit_records:delete') && permissions.value.delete
})
// 编辑
const showEditRecord = computed(() => {
    return hasPerms(menus, 'action:t_visit_records:edit') && permissions.value.edit
})
// 审核按钮
const showValidRecord = computed(() => {
    return permissions.value.valid && (recordDetail.value.state == 0 ? false : true)
})
// 分享
const showShareByPerm = computed(() => {
    return hasPerms(menus, 'action:t_visit_records_share:add') && !permissions.value.valid && permissions.value.share
})
// 分享删除
const showShareDelByPerm = computed(() => {
    return hasPerms(menus, 'action:t_visit_records_share:delete') && !permissions.value.valid && permissions.value.share
})
// 最终提交
const showFinalSubmit = computed(() => {
    return (recordDetail.value.state == 0 ? true : false) && !permissions.value.valid && permissions.value.finalSubmit
})


onLoad((opts) => {
    console.log(opts);
    // 详情ID
    recordId.value = opts.id
    // 权限按钮设置
    if (opts.p) {
        let pers = null
        // #ifdef H5
        pers = decrypt(decodeURIComponent(opts.p))
        // #endif
        // #ifdef MP-WEIXIN
        pers = JSON.parse(decodeURIComponent(opts.p))
        // #endif
        console.log(pers);
        permissions.value = pers
    }
    // 获取详情----
    getDetail()
})

const audioPlayUrls = ref([])
// 获取数据详情
const getDetail = () => {
    getVisitRecordById({ id: recordId.value }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            let data = res.result
            // 图片视频处理
            if (data.clientGatePics) {
                let pics = JSON.parse(data.clientGatePics)
                data.clientGatePics = pics.map(item => ({
                    url: item
                }))
            }
            if (data.wechatScreenshotPics) {
                let pics = JSON.parse(data.wechatScreenshotPics)
                data.wechatScreenshotPics = pics.map(item => ({
                    url: item
                }))
            }
            if (data.clientVideo) {
                let pics = JSON.parse(data.clientVideo)
                data.clientVideo = pics.map(item => ({
                    url: item
                }))
            }
            // 录音处理audioPlayUrls
            if (data.audioData) {
                let audio = JSON.parse(data.audioData)
                audioPlayUrls.value = audio
            }
            // 提交状态设置按钮---状态为最终提交时不能删除
            if (data.state == global.marketActionVisitState.find(item => item.label == '最终提交').value) {
                permissions.value.delete = false
            }

            // 设置拜访类型
            recordVisitType.value = res.result.visitType
            // 设置数据------------------------------
            recordDetail.value = res.result

            // console.log(recordDetail.value);

            // 查询站点促销列表
            if (data.visitType == vt.salesAchievements) {
                getVisitSalesAchievement({ recordId: recordId.value }).then((res: any) => {
                    console.log(res);
                    if (res.code == 200) {
                        recordDetail.value.visitSalesAchievements = res.result
                    }
                })
            }
            // 查询visitTypeInfo
            getVisitTypeInfoById({ recordId: recordId.value }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    recordDetail.value.visitTypeInfo = res.result
                    console.log(recordDetail.value);

                }
            })
            // 查询分享人员
            getShare()
            // 查询同行人员
            getMyPartnerList({ recordsId: recordId.value }).then((res: any) => {
                // console.log(res);
                if (res.code == 200) {
                    visitPartners.value = res.result
                }
            })
        }
    })
}
// 跳转编辑
const toEdit = () => {
    console.log(recordDetail.value);
    console.log(JSON.stringify(recordDetail.value));
    console.log(encodeURIComponent(JSON.stringify(recordDetail.value)));

    router.push({
        path: '/pages-service/marketAction/marketActionAddRecord',
        query: {
            action: 'edit',
            editData: encodeURIComponent(JSON.stringify(recordDetail.value)),
            editDataPartners: encodeURIComponent(JSON.stringify(visitPartners.value))
        }
    })
}
// 跳转删除
const toDel = () => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delMarketActionRecord({
                id: recordId.value
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    router.back()
                }
            })
        })
}
// 审核
const toValid = () => {
    validBox.confirm({
        title: '审核意见'
    }).then(() => {
        if (validPass.value == -1) {
            toast.info('请选择是否通过')
            return
        }
        console.log(validText.value, validPass.value);
        auditVisit({
            id: recordId.value,
            auditOpinion: validText.value,
            auditStatus: validPass.value
        }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                let permission = null
                // #ifdef H5
                permission = encodeURIComponent(encrypt({
                    add: false,
                    edit: false,
                    valid: false,
                    share: true,
                    finalSubmit: false
                }))
                // #endif
                // #ifdef MP-WEIXIN
                permission = encodeURIComponent(JSON.stringify({
                    add: false,
                    edit: false,
                    valid: false,
                    share: true,
                    finalSubmit: false
                }))
                // #endif
                router.replace({
                    path: '/pages-service/marketAction/marketActionCheckSingleRecord',
                    query: {
                        id: recordId.value,
                        p: permission
                    }
                })
            } else {
                toast.error(res.message)
            }
        })
    }).catch((error) => {
        console.log(error)
    })
}
// 最终提交
const toFinalSubmit = () => {
    finalSubmission({
        id: recordId.value,
        state: 1
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            router.back()
        } else {
            toast.error(res.message)
        }
    })
}
// 获取分享人员
const getShareList = () => {
    getUserTenantPageList({
        username: '',//工号
        realname: searchShareValue.value,//姓名
        pageSize: 100,
        userTenantStatus: 1
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            shareColumns.value = res.result.records.map(item => ({
                value: item.id,
                label: `${item.realname} - ${item.workNo}`,
                workNo: item.workNo,
                name: item.realname
            }))
            shareValue.value = shareColumns.value[0].value
            // console.log(shareColumns.value, shareValue.value);
        }
    })
}
// 搜索值
const searchShareValue = ref('')
// 搜索分享人
const searchShare = (e) => {
    console.log(e);
    getShareList()
}
// 分享相关
const showShare = ref(false)
const shareColumns = ref([])
const shareValue = ref('')
const onChangeShare = ({ picker, value, index }) => {
    shareValue.value = value
}
// 关闭选择器
const closeShare = () => {
    showShare.value = false
}
// 打开选择器
const showShareSomeone = () => {
    showShare.value = true
    getShareList()
}
// 添加分享
const handleShareOne = () => {
    console.log(shareColumns.value, shareValue.value);
    let data = {
        recordsId: recordId.value,
        userAccount: shareColumns.value.find(item => item.value == shareValue.value).workNo
    }
    console.log(data);

    addSharePeople(data).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            toast.success('分享成功！')
            showShare.value = false
            getShare()
        }
    })
}
// 删除分享
const delShare = (e) => {
    delSharePeople({
        id: e.id
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            toast.success('删除成功！')
            getShare()
        }
    })
}
// 获取分享人员
const getShare = () => {
    // 查询分享人员---------------------------------------------------------------
    getMyShareList({ recordsId: recordId.value }).then((res: any) => {
        // console.log(res);
        if (res.code == 200) {
            visitShares.value = res.result
        }
    })
}

const playAudio = (element) => {
    // #ifdef H5
    playAudioH5(element)
    // #endif
    // #ifdef MP-WEIXIN
    playAudioMPWX(element)
    // #endif
}


// 播放音频
let innerAudioContext = null
const playAudioMPWX = (element) => {
    if (innerAudioContext) {
        innerAudioContext.pause()
        innerAudioContext.destroy()
        innerAudioContext = null
        innerAudioContext = uni.createInnerAudioContext()
        console.log('销毁');

    } else {
        innerAudioContext = uni.createInnerAudioContext()
    }

    innerAudioContext.onPlay(() => {
        console.log('开始播放');
    });
    innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
    });
    innerAudioContext.onEnded(() => {
        console.log('自然播放结束');
        element.playing = false
    })

    innerAudioContext.src = element.url;
    innerAudioContext.play()
    audioPlayUrls.value.forEach(e => {
        e.playing = false
    })
    element.playing = true
}

// H5播放音频
const audioPlayer = ref(null)
const playAudioH5 = (element) => {
    // 如果已有实例，先销毁
    if (audioPlayer.value) {
        audioPlayer.value.pause()
        audioPlayer.value.src = ''
        audioPlayer.value = null
    }

    // 创建新的 Audio 实例
    audioPlayer.value = new Audio(element.url)

    // 添加事件监听
    // audioPlayer.value.addEventListener('loadedmetadata', () => {
    //     duration = audioPlayer.duration
    // })

    // audioPlayer.addEventListener('timeupdate', () => {
    //     currentTime = audioPlayer.currentTime
    // })

    audioPlayer.value.addEventListener('play', () => {
        // isPlaying = true
    })

    // audioPlayer.addEventListener('pause', () => {
    //     isPlaying = false
    // })

    audioPlayer.value.addEventListener('ended', () => {
        element.playing = false
        console.log('播放结束');
        // isPlaying = false
        // currentTime = 0
    })

    audioPlayer.value.addEventListener('error', (e) => {
        console.error('音频播放错误:', e)
        // isPlaying = false
    })

    audioPlayer.value.play()
    audioPlayUrls.value.forEach(e => {
        e.playing = false
    })
    element.playing = true

}


const gethms = (timestamp) => {
    // 将时间戳转换为毫秒数
    const milliseconds = parseInt(timestamp, 10);
    // 计算总分钟数
    const totalMinutes = Math.floor(milliseconds / 60000);
    // 计算小时数和剩余的分钟数
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const seconds = minutes % 60;
    // 返回格式化的字符串
    return `${hours}小时${minutes}分钟${seconds}秒`;
}
</script>
<style lang="scss" scoped></style>