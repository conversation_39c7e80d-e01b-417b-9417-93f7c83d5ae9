<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '阶段任务下达',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout backRouteName="index" navTitle="阶段任务下达" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white">
                <wd-search hide-cancel custom-class="w55%" placeholder="输入群名称查询" v-model="searchGroupName"
                    @search="handleSearch" @clear="handleSearch" />
                <wd-calendar type="month" v-model="assignDate" use-default-slot @confirm="confirmAssignDate">
                    <wd-button icon="calendar" plain>
                        {{ dayjs(assignDate).format('YYYY-MM') }}
                    </wd-button>
                </wd-calendar>
                <wd-icon name="add-circle" color="#0083ff" size="28px" custom-class="ml3" @click="toAdd" />
            </view>
            <view class="ml3 mr3 mb3">
                <wd-table :data="assignTable" :height="'70vh'" @row-click="assignTableClick" :fixed-header="false">
                    <wd-table-col prop="groupName" label="群名称" :width="80" fixed></wd-table-col>
                    <wd-table-col prop="firstVisitNum" label="首次拜访" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="undealVisitNum" label="未重拜" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="dealVisitNum" label="已重拜" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="biologicalTestsNum" label="生测实验" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="modelFieldNum" label="示范田" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="orderMeetingNum" label="订货会" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="observationMeetingNum" label="观摩会" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="noteNum" label="工作笔记" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="pullTrainingNum" label="拉练" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="promotionalNum" label="站点促销" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="productPromotionNum" label="产品宣传" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="trainingNum" label="培训" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="regularMeetingNum" label="例会" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="farmersWillNum" label="农民会" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="120">
                        <template #value="{ row }">
                            <wd-button size="small" custom-class="" @click.stop="toEdit(row)">修改</wd-button>
                            <wd-button type="error" size="small" custom-class="" @click.stop="toDel(row)">删除</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="assignTablePageNo"
                    :pageSize="assignTablePageSize" :total="assignTableTotal"
                    @change="assignTablePageChange"></wd-pagination>
                <wd-status-tip v-if="assignTable.length == 0" image="search" tip="当前搜索无结果" />
            </view>
        </view>
    </PageLayout>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { getMissionAssignList, delMissionAssign } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const colWidth = ref(50)

// 搜索
const searchGroupName = ref('')
const handleSearch = () => {
    getAssignTable()
}
// 日期
const assignDate = ref<number>(Date.now())
const confirmAssignDate = (e) => {
    console.log(e);
    getAssignTable()
}
// 表格
const assignTable = ref([])
const assignTableClick = (e) => {
    console.log(e);
    console.log(assignTable.value);
    
    router.push({
        path: '/pages-service/mission/missionAssignAdd',
        query: {
            action: 'check',
            id: assignTable.value[e.rowIndex].id
        }
    })
}
// 分页
const assignTablePageNo = ref<number>(1)
const assignTablePageSize = ref<number>(10)
const assignTableTotal = ref<number>(assignTable.value.length)
const assignTablePageChange = (e) => {
    assignTablePageNo.value = e.value
    getAssignTable()
}

const getAssignTable = () => {
    getMissionAssignList({
        taskTime: dayjs(assignDate.value).format('YYYY-MM'),
        pageNo: assignTablePageNo.value,
        pageSize: assignTablePageSize.value,
        groupName: searchGroupName.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            assignTable.value = res.result.records
        }
    })
}

const toAdd = (e) => {
    router.push({
        path: '/pages-service/mission/missionAssignAdd',
        query: {
            action: 'add'
        }
    })
}

const toEdit = (e) => {
    console.log(e);
    router.push({
        path: '/pages-service/mission/missionAssignAdd',
        query: {
            action: 'edit',
            id: e.id
        }
    })
}

const toDel = (e) => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delMissionAssign({
                id: e.id
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    getAssignTable()
                }
            })
        })
}

onLoad(() => {
    getAssignTable()
    uni.$on('refreshList', (e) => {
        toast.success(e)
        getAssignTable()
    })
})
onUnload(() => {
    uni.$off('refreshList')
})
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>