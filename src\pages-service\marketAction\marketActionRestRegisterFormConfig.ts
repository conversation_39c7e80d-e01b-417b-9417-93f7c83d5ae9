import { FormRules } from "wot-design-uni/components/wd-form/types"

const formConfig = ref([
    {
        title: '',
        form: [
            {
                type: 'calendar',
                label: '开始日期',
                name: 'startTime',
                pickerType: 'date'
            },
            {
                type: 'calendar',
                label: '结束日期',
                name: 'endTime',
                pickerType: 'date'
            },
            {
                type: 'text',
                label: '时长',
                name: 'duration',
            },
            {
                type: 'textarea',
                label: '备注',
                name: 'remark',
                placeholder: '请输入备注'
            }
        ]
    }
])

const rules: FormRules = {
    // taskTime: [
    //     {
    //         required: true,
    //         message: '请选择时间',
    //         trigger: ['blur', 'change']
    //     }
    // ]
}

export { formConfig, rules };