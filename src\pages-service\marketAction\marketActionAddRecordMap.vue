<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '地图',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
  <PageLayout :navTitle="customTitle" backRouteName="index" routeMethod="replaceAll" :showFab="false">
    <!-- H5 -->
    <!-- H5 -->
    <!-- H5 -->
    <!-- #ifdef H5 -->
    <view class="flex justify-around pt2 pb2 w100%" style="background-color: #ffffff;">
      <!-- <wd-button customClass="w45% ml2 mr2" @click="showPointType = true">{{ pointTypeActions[pointTypeIndex].name
      }}</wd-button>
      <wd-action-sheet v-model="showPointType" :actions="pointTypeActions" @close="showPointType = false"
        @select="selectPointType" /> -->
      <wd-calendar v-model="traDate" label="" @confirm="getTra" use-default-slot custom-class="w90%" :min-date="minDate"
        :max-date="maxDate">
        <wd-button icon="calendar" customClass="w100% ml2 mr2">选择日期：{{ `${dayjs(traDate).format('YYYY-MM-DD')}`
        }}</wd-button>
      </wd-calendar>
      <!-- <wd-button icon="add" customClass="ml2 mr2" type="success" @click="toAddBX">添加报销</wd-button> -->
    </view>
    <view class="flex justify-around pb2 w100% bg-#ffffff">
      <view class="flex">
        <view>总里程：</view>
        <view :class="allDistance > 15 ? 'green' : 'red'" class="font-bold">{{ allDistance }}</view>
        <view>km</view>
      </view>
      <view>总时长：{{ allTime }}</view>
    </view>
    <!-- 地图 -->
    <view id="container" class="w100% h90vh"></view>
    <!-- #endif -->

    <!-- 微信-------------------------------------------- -->
    <!-- 微信-------------------------------------------- -->
    <!-- 微信-------------------------------------------- -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="flex justify-around pt2 pb1 w100% bg-#ffffff">
      <!-- <wd-button customClass="w45% ml2 mr2" @click="showPointType = true">{{ pointTypeActions[pointTypeIndex].name
      }}</wd-button>
      <wd-action-sheet v-model="showPointType" :actions="pointTypeActions" @close="showPointType = false"
        @select="selectPointType" /> -->
      <wd-calendar v-model="traDate" label="" @confirm="getTraWX" use-default-slot custom-class="w60% z-9999"
        :min-date="minDate" :max-date="maxDate">
        <wd-button icon="calendar" customClass="w100% ml2 mr2">{{ `${dayjs(traDate).format('YYYY-MM-DD')}`
        }}</wd-button>
      </wd-calendar>
      <wd-button icon="add" customClass="ml2 mr2" type="success" @click="toAddBX">添加报销</wd-button>
    </view>
    <view class="flex justify-around pb2 w100% bg-#ffffff">
      <view class="flex">
        <view>总里程：</view>
        <view :class="allDistance > 15 ? 'green' : 'red'" class="font-bold">{{ allDistance }}</view>
        <view>km</view>
      </view>
      <view>总时长：{{ allTime }}</view>
    </view>
    <map class="w100% h80vh" id="wxmap" ref="wxmap" :latitude="latitudeWX" :longitude="longitudeWX"
      :polyline="polylineWX" @markertap="markerTap" :scale="scaleWX" @tap="tapmap" :markers="markersWX" @callouttap="callouttap">



      <cover-view slot="callout" style="z-index: 9999;">
        <cover-view class="w300px p2 bg-white" :marker-id="item.id" v-for="(item, index) in markersWX" :key="index">
          <cover-view class="font-size-4">
            <cover-view class="mt1 mb1 flex">
              <cover-view class="mr1">开始时间 :</cover-view>
              <cover-view class="">{{ item.startDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex">
              <cover-view class="mr1">结束时间 :</cover-view>
              <cover-view class="">{{ item.endDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex items-center">
              <cover-view class="mr1">停留 :</cover-view>
              <cover-view class="bg-#e3742a color-#ffffff rd-3 pt1 pb1 pl2 pr2">{{ item.stayDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex">
              <cover-view class="mr1">地点 :</cover-view>
              <cover-view class="w250px" style="white-space: pre-wrap">{{ item.address }}</cover-view>
            </cover-view>
            <cover-view @click="toaddWX(item)" class="bg-#dda129 rd-1.5 color-#ffffff pt1 pb1 text-center mt2 mb2">
              营销动作({{ visitNum }})
            </cover-view>
          </cover-view>
        </cover-view>
      </cover-view>

      <!-- <cover-view v-if="showBottomContent" class="p4 pb6 bg-white z-10">
        <cover-view class="max-w90% ">
          <cover-view class="font-size-4">
            <cover-view
              class="position-absolute right-15 w12 h5 line-height-5 text-center bg-amber color-#ffffff rd-1 font-size-3"
              @click="setCoverClose">关闭</cover-view>
            <cover-view class="mt1 mb1 flex w100%">
              <cover-view class="mr1">开始时间 :</cover-view>
              <cover-view class="">{{ infoWX.startDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex w100%">
              <cover-view class="mr1">结束时间 :</cover-view>
              <cover-view class="">{{ infoWX.endDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex items-center">
              <cover-view class="mr1">停留 :</cover-view>
              <cover-view class="bg-#e3742a color-#ffffff rd-3 pt1 pb1 pl2 pr2">{{ infoWX.stayDate }}</cover-view>
            </cover-view>
            <cover-view class="mt1 mb1 flex">
              <cover-view class="mr1">地点 :</cover-view>
              <cover-view class="" style="white-space: pre-wrap">{{ infoWX.address }}</cover-view>
            </cover-view>
            <cover-view @click="toaddWX(infoWX)" class="bg-#dda129 rd-1.5 color-#ffffff pt1 pb1 text-center mt2 mb2">
              营销动作({{ visitNum }})
            </cover-view>
          </cover-view>
        </cover-view>
      </cover-view> -->

      <cover-view class="absolute right-4 top-4 bg-#ffffff rd-2">
        <cover-view class="p2 text-center font-size-6 border-b-black border-b" @click="fandda">＋</cover-view>
        <cover-view class="pl2 pr2 pb1 text-center font-size-8" @click="suoxiao">-</cover-view>
      </cover-view>


    </map>
    <!-- #endif -->
  </PageLayout>
</template>


<script lang="ts" setup>
import AMapLoader from '@amap/amap-jsapi-loader';
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router';
import { useUserStore } from '@/store';
import { queryUserTrajectoryByDate, queryVisitRecordsNumber } from '@/service/marketAction/marketAction'

const router = useRouter()
const toast = useToast();
const userInfo = useUserStore().userInfo
const customTitle = ref('')
const allTime = ref('')
const allDistance = ref(0)
customTitle.value = userInfo.realname + '的轨迹'
const visitNum = ref(0)
const minDate = Date.now() - 1 * 31536000000
const maxDate = Date.now()
// 日期选择-----------------------------------------
const traDate = ref<number>(Date.now())
// 点位显示状态---------------------------------------
const showPointType = ref<boolean>(false)
const pointTypeIndex = ref(0)
const pointTypeActions = ref([
  { name: '显示全部', id: 0 },
  { name: '显示起点', id: 1 },
  { name: '显示经过', id: 2 },
  { name: '显示终点', id: 3 }
])
const selectPointType = (e) => {
  console.log(e);
  pointTypeIndex.value = e.index
  getTra(e.index)
}
// 跳转报销
const toAddBX = () => {
  router.push({
    path: '/pages-service/marketAction/marketActionReimburse',
    query: {
      date: dayjs(traDate.value).format('YYYY-MM-DD')
    }
  })
}

// 获取时分秒
const gethms = (timestamp, delsec = false) => {
  // 将时间戳转换为毫秒数
  const milliseconds = parseInt(timestamp, 10);
  // 计算总分钟数
  const totalMinutes = Math.floor(milliseconds / 60000);
  // 计算小时数和剩余的分钟数
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const seconds = minutes % 60;
  // 返回格式化的字符串
  if (delsec) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${hours}小时${minutes}分钟${seconds}秒`;
  }
}














// #ifdef MP-WEIXIN

const scaleWX = ref(12)
const latitudeWX = ref(22.833900)
const longitudeWX = ref(108.313000)
const markersWX = ref([])
const polylineWX = ref([])

const showBottomContent = ref(false)
const infoWX = ref({})

const wxmap = uni.createMapContext('wxmap')
console.log(wxmap);
wxmap.initMarkerCluster({
  enableDefaultStyle: true,
  zoomOnClick: true,
  gridSize: 60
})


// 加载--------------------
onLoad(async (opts) => {
  console.log(opts);
  if (opts.date) {
    traDate.value = Number(opts.date)
  }
  getTraWX()
})
const getTraWX = (showType = 0) => {
  queryUserTrajectoryByDate({
    date: dayjs(traDate.value).format('YYYY-MM-DD'),
    znkqUserId: userInfo.znkqId,
  }).then((res: any) => {
    console.log(res);
    if (res.code == 200) {

      allDistance.value = res.result.distance / 1000
      allTime.value = gethms(res.result.time, true)

      let points = JSON.parse(res.result.points)
      console.log(points);

      if (points && points.length > 0) {
        // latitudeWX.value = points[0].lat
        // longitudeWX.value = points[0].lng

        // 计算地图中心
        let center = calculateSphericalCentroid(points.map(item => ({
          lat: item.lat,
          lng: item.lng
        })))
        console.log(center);
        // 设置地图中心
        latitudeWX.value = center.lat
        longitudeWX.value = center.lng

        markersWX.value = []
        polylineWX.value = []
        let lines = []
        for (var i = 0; i < points.length; i++) {
          let point = points[i]
          lines.push({ latitude: point.lat, longitude: point.lng })
          // 经过
          if (i != 0 && i != points.length - 1 && point.info != null && showType != 2) {

            let timeData = point?.info[0]
            let stayDate = gethms(timeData.endTime - timeData.startTime)
            let startDate = dayjs(timeData.startTime).format('MM月DD日 HH时mm分')
            let endDate = dayjs(timeData.endTime).format('MM月DD日 HH时mm分')
            let address = point.address

            let marker = {
              ...point,
              id: point.id,
              longitude: point.lng,
              latitude: point.lat,
              width: 60,
              height: 60,
              iconPath: '/static/tra/via.png',
              customCallout: {
                display: "BYCLICK",
                anchorX: 0,
                anchorY: 0
              },
              stayDate,
              startDate,
              endDate,
              address
            }
            markersWX.value.push(marker)
          }
          // console.log(markersWX.value);
        }

        // 路径
        polylineWX.value.push({
          points: lines,
          color: '#11acff',
          width: 6,
          arrowLine: true,
          // borderColor: '#149a2f',
          // borderWidth: 1,
          dottedLine: false
        })

        // 起点
        let start = points[0]
        if (showType != 1) {

          let timeData = start?.info[0]
          let stayDate = gethms(timeData.endTime - timeData.startTime)
          let startDate = dayjs(timeData.startTime).format('MM月DD日 HH时mm分')
          let endDate = dayjs(timeData.endTime).format('MM月DD日 HH时mm分')
          let address = start.address

          let marker = {
            ...start,
            id: start.id,
            longitude: start.lng,
            latitude: start.lat,
            width: 60,
            height: 60,
            iconPath: '/static/tra/start.png',
            customCallout: {
              display: "BYCLICK",
              anchorX: 0,
              anchorY: 0
            },
            stayDate,
            startDate,
            endDate,
            address
          }
          markersWX.value.push(marker)
        }
        // 终点
        let end = points[points.length - 1]
        if (showType != 3) {

          let timeData = end?.info[0]
          let stayDate = gethms(timeData.endTime - timeData.startTime)
          let startDate = dayjs(timeData.startTime).format('MM月DD日 HH时mm分')
          let endDate = dayjs(timeData.endTime).format('MM月DD日 HH时mm分')
          let address = end.address

          let marker = {
            ...end,
            id: end.id,
            longitude: end.lng,
            latitude: end.lat,
            width: 60,
            height: 60,
            iconPath: '/static/tra/end.png',
            customCallout: {
              display: "BYCLICK",
              anchorX: 0,
              anchorY: 0
            },
            stayDate,
            startDate,
            endDate,
            address
          }
          markersWX.value.push(marker)
        }
        selectVisitCount()


        // wxmap.addMarkers({
        //   markers: markersWX.value,
        //   clear: true
        // })

        setTimeout(() => {
          setFitViewWX()
        }, 333);

      } else {
        toast.info('无轨迹')
      }
    }
  })
}

// 点击标记点
const markerTap = (e) => {
  console.log(e);
  if (!e.markerId) {
    return
  }

  let data = markersWX.value.find(item => item.id == e.markerId)
  if (data.customCallout.display == 'ALWAYS') {
    data.customCallout.display = 'BYCLICK'
    return
  }

  for (const i in markersWX.value) {
    let ele = markersWX.value[i]
    if (ele.id == e.markerId) {
      ele.customCallout.display = 'ALWAYS'
    } else {
      ele.customCallout.display = 'BYCLICK'
    }
  }

  // if (e.markerId == infoWX.value.id) {
  //   showBottomContent.value = !showBottomContent.value
  //   return
  // }

  // infoWX.value = markersWX.value.find(item => item.id == e.markerId)
  // showBottomContent.value = true

}

// 点击地图
const tapmap = (e) => {
  // console.log(e);
  // for (const i in markersWX.value) {
  //   let ele = markersWX.value[i]
  //   ele.customCallout.display = 'BYCLICK'
  // }
  // showBottomContent.value = false
}

const callouttap = (e) => {
  console.log(e);
  let item = markersWX.value.find(item => item.id == e.markerId)
  router.push({
    name: 'marketActionAddRecord',
    params: {
      ...item,
      action: 'add',
      info0startTime: item?.info[0]?.startTime,
      info0endTime: item?.info[0]?.endTime
    }
  })
}

const toaddWX = (item) => {
  router.push({
    name: 'marketActionAddRecord',
    params: {
      ...item,
      action: 'add',
      info0startTime: item?.info[0]?.startTime,
      info0endTime: item?.info[0]?.endTime
    }
  })
}

const fandda = () => {
  if (scaleWX.value < 20 && scaleWX.value >= 3) {
    scaleWX.value++
  }
}
const suoxiao = () => {
  if (scaleWX.value > 3 && scaleWX.value <= 20) {
    scaleWX.value--
  }
}

const setFitViewWX = () => {
  wxmap.includePoints({
    points: markersWX.value,
    padding: [60, 40, 60, 40] // 上右下左的padding
  });
}

const setCoverClose = () => {
  showBottomContent.value = false
}

// #endif


























// #ifdef H5
const map = ref(null);
const loaderAMap = ref(null);
const zoom = ref(12)
const latitude = ref(22.833900)
const longitude = ref(108.313000)
const markerStartContent = ref('<view class="marker startPNG"></view>')
const markerViaContent = ref('<view class="marker viaPNG"></view>')
const markerEndContent = ref('<view class="marker endPNG"></view>')
const mapMarkerOffsetX = ref(-25)
const mapMarkerOffsetY = ref(-45)
// 加载--------------------------------------------
onLoad(async (opts) => {
  console.log(opts);

  if (opts.date) {
    traDate.value = Number(opts.date)
  }
  await initMap()
  getTra()
})
// 地图---------------------------------------------------
const initMap = async () => {
  return new Promise((resolve, reject) => {
    (window as any)._AMapSecurityConfig = {
      securityJsCode: "b1b170ac8a0d56e04dad3478ea8f1f9d",
    };
    AMapLoader.load({
      key: "f8aabaa343608b6efdbf4c2e48e6d8b3", // 申请好的Web端开发者Key，首次调用 load 时必填
      version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
    })
      .then((AMap) => {
        loaderAMap.value = AMap
        map.value = new AMap.Map("container", {
          resizeEnable: true,
          center: [longitude.value, latitude.value],
          zoom: zoom.value
        });
        AMap.plugin('AMap.ToolBar', function () {
          let toolbar = new AMap.ToolBar(); //缩放工具条实例化
          map.value.addControl(toolbar); //添加控件
        });
        resolve(1)
      })
      .catch((e) => {
        console.log(e);
      });
  })
}

// 信息窗体
const infoWindow = (e) => {
  console.log(e);
  // let clickedMarker = e.target;
  // // 设置被点击的marker的zindex
  // clickedMarker.setOptions({
  //   zIndex: 20
  // });

  let pointInfo = e.target.getExtData()
  console.log(pointInfo);
  let timeData = pointInfo.info[0];
  let stayDate = gethms(timeData.endTime - timeData.startTime)
  // let stayDate = timestampDifference(timeData.startTime, timeData.endTime)
  let startDate = dayjs(timeData.startTime).format('MM月DD日 HH时mm分')
  let endDate = dayjs(timeData.endTime).format('MM月DD日 HH时mm分')

  // infowidnow 的 innerHTML
  let infoWindowContent =
    `<div class ="mapWindow">
						<div class = "mapWindowTxt">
							<div class="txt">开始时间 : <span class="address">${startDate}</span></div>
							<div class="txt">结束时间 : <span class="address">${endDate}</span></div>
							<div class="txt">停留 : <span class="stay">${stayDate}</span></div>
							<div class="txt">地点 : <span class="address">${pointInfo.address}</span></div>
							<div id="toadd" class = "infoClick">营销动作(${visitNum.value})</div>
						</div>
					</div>`;
  let AMap = loaderAMap.value
  // 创建一个自定义内容的 infowindow 实例
  let infoWindow = new AMap.InfoWindow({
    position: new AMap.LngLat(pointInfo.lng, pointInfo.lat),
    // position: new AMap.LngLat(pointInfo.lng, pointInfo.lat),
    offset: new AMap.Pixel(0, -30),
    content: infoWindowContent
  });
  // 设置中心点与window有冲突，需要延迟执行
  setTimeout(function () {
    map.value.setCenter([pointInfo.lng, pointInfo.lat])
  }, 100);
  infoWindow.open(map.value);
  // 添加点击事件-------------------------
  let windowDom = document.getElementById('toadd')
  windowDom.addEventListener('click', () => {
    infoWindow.close()
    router.push({
      name: 'marketActionAddRecord',
      params: {
        ...pointInfo,
        action: 'add',
        info0startTime: timeData.startTime,
        info0endTime: timeData.endTime
      }
    })
  })
}

// 查询轨迹
const getTra = (showType = 0) => {
  console.log('showType' + showType);
  map.value.clearMap()
  queryUserTrajectoryByDate({
    date: dayjs(traDate.value).format('YYYY-MM-DD'),
    znkqUserId: userInfo.znkqId,
    //date: '2025-02-12',
    //znkqUserId: 6139
  }).then((res: any) => {
    console.log(res);
    if (res.code == 200) {

      allDistance.value = res.result.distance / 1000
      allTime.value = gethms(res.result.time, true)

      let points = JSON.parse(res.result.points)
      console.log(points);
      if (points && points.length > 0) {
        let Amap = loaderAMap.value
        // 经过----------------------------------------
        let lines = []
        for (var i = 0; i < points.length; i++) {
          let point = points[i]
          // 可设置点类型（起点经过终点），再存入extData
          lines.push([point.lng, point.lat])
          if (i != 0 && i != points.length - 1 && point.info != null && showType != 2) {
            let viaMarker = new Amap.Marker({
              position: new Amap.LngLat(point.lng, point.lat),
              content: markerViaContent.value,
              offset: new Amap.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
              extData: point,
              title: '经过'
            })
            viaMarker.on('click', infoWindow)
            map.value.add(viaMarker)
          }
        }
        // 起点----------------------------------------
        let start = points[0]
        if (showType != 1) {
          let startMarker = new Amap.Marker({
            position: new Amap.LngLat(start.lng, start.lat),
            content: markerStartContent.value,
            offset: new Amap.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
            extData: start,
            title: '起点'
          })
          startMarker.on('click', infoWindow)
          map.value.add(startMarker)
        }
        // 终点-----------------------------------------
        let end = points[points.length - 1]
        if (showType != 3) {
          let endMarker = new Amap.Marker({
            position: new Amap.LngLat(end.lng, end.lat),
            content: markerEndContent.value,
            offset: new Amap.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
            extData: end,
            title: '终点'
          })
          endMarker.on('click', infoWindow)
          map.value.add(endMarker)
        }
        // 轨迹-----------------------------------------
        let polyline = new Amap.Polyline({
          map: map.value,
          path: lines,
          showDir: true, // 箭头
          strokeColor: "#149a2f", // 线颜色
          strokeOpacity: 0.5, //线透明度
          strokeWeight: 6, // 线宽
          strokeStyle: "solid", // 线样式
          lineJoin: 'round',
          lineCap: 'round',
        });
        map.value.setFitView();
        selectVisitCount()
      } else {
        toast.info('无轨迹')
      }
    }
  })
}

// #endif
















// 查询营销动作次数----------------
const selectVisitCount = () => {
  let param = {
    date: dayjs(traDate.value).format('YYYY-MM-DD')
  }
  queryVisitRecordsNumber(param).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      visitNum.value = res.result
      console.log(visitNum.value);
    }
  })
}

// 计算地图中心点
const calculateSphericalCentroid = (points) => {
  if (!points || points.length === 0) return null;

  let x = 0, y = 0, z = 0;

  points.forEach(point => {
    // 将经纬度转换为弧度
    const latRad = point.lat * Math.PI / 180;
    const lngRad = point.lng * Math.PI / 180;

    // 转换为三维直角坐标
    x += Math.cos(latRad) * Math.cos(lngRad);
    y += Math.cos(latRad) * Math.sin(lngRad);
    z += Math.sin(latRad);
  });

  const numPoints = points.length;
  x /= numPoints;
  y /= numPoints;
  z /= numPoints;

  // 转换回经纬度
  const hyp = Math.sqrt(x * x + y * y);
  const latRad = Math.atan2(z, hyp);
  const lngRad = Math.atan2(y, x);

  return {
    lat: latRad * 180 / Math.PI,
    lng: lngRad * 180 / Math.PI
  };
}


</script>

<style lang="scss" scoped>
::v-deep .amap-logo {
  display: none !important;
}

::v-deep .amap-copyright {
  display: none !important;
}

// 信息窗体
::v-deep .mapWindow {
  width: 300px;
  padding: 0 6rpx;
  background-color: white;

  // height: 100px;
  .mapWindowTxt {
    font-size: 30rpx;
    margin: 6rpx 0;
  }

  .txt {
    margin: 4px 0;
  }

  .infoClick {
    background-color: #dda129;
    border-radius: 12rpx;
    color: white;
    padding: 8rpx 0;
    text-align: center;
    margin: 16rpx auto;
  }

  .time {
    background-color: #cabf94;
    border-radius: 12px;
    padding: 4px 8px;
  }

  .stay {
    background-color: #e3742a;
    color: #ffffff;
    border-radius: 12px;
    padding: 4px 8px;
  }
}

::v-deep .marker {
  width: 50px;
  height: 50px;
  display: block;
  // opacity: .5;
}

::v-deep .startPNG {
  background: url('@/static/tra/start.png');
  background-size: contain;
}

::v-deep .viaPNG {
  background: url('@/static/tra/via.png');
  background-size: contain;
}

::v-deep .endPNG {
  background: url('@/static/tra/end.png');
  background-size: contain;
}

.green {
  // background-color: rgb(17, 161, 17);
  // color: white;
  color: rgb(21, 218, 31)
}

.red {
  // background-color: red;
  // color: white;
  color: red
}
</style>