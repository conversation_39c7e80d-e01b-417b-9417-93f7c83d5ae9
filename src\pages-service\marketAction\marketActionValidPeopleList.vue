<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '我的督查和审核',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="我的督查和审核">
        <view class="bg-#ffffff pb3">
            <view class="flex flex-justify-around flex-wrap flex-items-center w100% pt2">

                <wd-picker :columns="groupPickerColumns" v-model="groupPicker" @confirm="confirmGroupPicker"
                    use-default-slot custom-class="w45% mb2">
                    <wd-button icon="search" plain custom-class="w100%">
                        {{groupPickerColumns.length != 0 ? groupPickerColumns.find(item => item.value ==
                            groupPicker).label : ''}}
                    </wd-button>
                </wd-picker>

                <wd-picker :columns="personPickerColumns" v-model="personPicker" @confirm="confirmPersonPicker"
                    use-default-slot custom-class="w45% mb2">
                    <wd-button icon="search" plain custom-class="w100%">
                        {{personPickerColumns.length != 0 ? personPickerColumns.find(item => item.value ==
                            personPicker).label : ''}}
                    </wd-button>
                </wd-picker>

                <wd-calendar type="month" v-model="validDate" use-default-slot @confirm="confirmValidDate"
                    custom-class="w45%" :max-date="maxDate" :min-date="minDate">
                    <wd-button icon="calendar" plain custom-class="w100%">
                        {{ dayjs(validDate).format('YYYY-MM') }}
                    </wd-button>
                </wd-calendar>

                <view class="w45%">
                    <wd-button icon="search" plain @click.stop="showAuditState = true" custom-class="w100%">
                        {{auditStateActions.find(item => item.id == auditState)?.name}}
                    </wd-button>
                    <wd-action-sheet v-model="showAuditState" :actions="auditStateActions"
                        @close="showAuditState = false" @select="selectAuditState" />
                </view>

            </view>
            <view class="ml3 mr3 mt3" v-if="validTable.length != 0">
                <wd-table :data="validTable" :height="'70vh'" :fixed-header="false" @row-click="validTableClick">
                    <wd-table-col prop="clientName" label="客户" :width="70"></wd-table-col>
                    <wd-table-col prop="visitType_dictText" label="动作类型" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="format_startTime" label="日期" :width="100"></wd-table-col>
                    <wd-table-col prop="auditStatus_dictText" label="审核状态" :width="100">
                        <template #value="{ row }">
                            <wd-button type="text">
                                {{ row.auditStatus_dictText }}
                            </wd-button>
                        </template>
                    </wd-table-col>
                    <wd-table-col prop="state_dictText" label="提交状态" :width="80"></wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="page"
                    :pageSize="pageSize" :total="total" @change="changePage"></wd-pagination>
            </view>
            <wd-status-tip v-if="validTable.length == 0" image="search" tip="无结果" />
        </view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { getMyGroupList, getUserGroupList, getPeopleMarketActionList } from '@/service/marketAction/marketAction'
import { encrypt } from '@/utils/crypto'

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const router = useRouter()

const colWidth = ref(120)
const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

onLoad((opts) => {
    console.log(opts);

    // 设置审核状态
    if (opts.auditState) {
        auditState.value = Number(opts.auditState)
    }

    // 查询群组
    getMyGroupList({
        znkqUserId: userInfo.znkqId
    }).then((res: any) => {
        if (res.code == 200) {
            groupPickerColumns.value = res.result.map(item => ({
                ...item,
                value: Number(item.id),
                label: item.name
            }))
        }
    })
    groupPicker.value = Number(opts.groupId)

    // 查询群员
    getUserGroupList({
        groupId: opts.groupId
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            let data = res.result.map(item => ({
                ...item,
                value: item.userId,
                label: item.userName
            }))
            personPickerColumns.value = data
            console.log(personPickerColumns.value);

        }
    })
    personPicker.value = opts.userId

    // console.log(opts);
    // 日期
    validDate.value = Number(opts.date)
    // 类型
    visitType.value = opts.visitType
    visitTypeText.value = opts.visitType

    getTable()
})

// 拜访类型
const visitType = ref()
const visitTypeText = ref('')

// 群组选择
const groupPicker = ref(0)
const groupPickerColumns = ref([])
const confirmGroupPicker = (e) => {
    console.log(e);
    // 查询群员
    getUserGroupList({
        groupId: groupPicker.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            let data = res.result.map(item => ({
                ...item,
                value: item.userId,
                label: item.userName
            }))
            personPicker.value = data[0].value
            personPickerColumns.value = data
            console.log(personPickerColumns.value);
            getTable()
        }
    })
}

// 群员选择
const personPicker = ref(0)
const personPickerColumns = ref([])
const confirmPersonPicker = (e) => {
    console.log(e);
    getTable()
}

// 时间
const validDate = ref<number>(Date.now())
const confirmValidDate = (e) => {
    console.log(e);
    getTable()
}

// 审核状态(筛选)
const showAuditState = ref(false)
const auditState = ref(-1)
const auditStateActions = [
    { name: '未审核', id: 0 },
    { name: '人工审核通过', id: 1 },
    { name: '系统审核通过', id: 2 },
    { name: '人工审核不通过', id: 3 }
]
const selectAuditState = (e) => {
    console.log(e);
    auditState.value = e.item.id
    getTable()
}

// 表格
const validTable = ref([])
const validTableClick = (e) => {
    console.log(validTable.value[e.rowIndex]);

    // 当单据未审核的时候显示审核按钮
    let v = false
    if (auditState.value == 0) {
        v = true
    }

    let permission = null
    // #ifdef H5
    permission = encodeURIComponent(encrypt({
        add: false,
        edit: false,
        valid: v,
        share: false,
        finalSubmit: false
    }))
    // #endif
    // #ifdef MP-WEIXIN
    permission = encodeURIComponent(JSON.stringify({
        add: false,
        edit: false,
        valid: v,
        share: false,
        finalSubmit: false
    }))
    // #endif
    // 跳转记录详情
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: validTable.value[e.rowIndex].id,
            p: permission
        }
    })
}
const page = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)
const changePage = (e) => {
    console.log(e);
    page.value = e.value
    getTable()
}

// 获取数据
const getTable = () => {
    getPeopleMarketActionList({
        date: dayjs(validDate.value).format('YYYY-MM'),
        pageNo: page.value,
        pageSize: pageSize.value,
        userId: personPicker.value,
        visitType: visitTypeText.value,
        auditStatus: auditState.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            validTable.value = res.result.records.map(item => ({
                ...item,
                format_startTime: dayjs(item.startTime).format('YYYY-MM-DD')
            }))
            total.value = res.result.total
        }
    })
}
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>