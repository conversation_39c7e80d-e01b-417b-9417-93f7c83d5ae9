<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '选择人员',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="选择群组" :showFab="false">
        <scroll-view scroll-y>
            <view class="pt3 pl3 pr3 pb3 bg-#ffffff">
                <wd-search v-model="searchValue" hide-cancel @search="search" placeholder="输入姓名搜索" />
                <!-- <wd-checkbox-group v-model="values" @change="change" :inline="false">
                    <wd-checkbox v-for="item in partners" v-model="item.id" size="large" shape="square"
                        :disabled="item.disabled">{{ item.label
                        }}</wd-checkbox>
                </wd-checkbox-group> -->
                <wd-radio-group v-model="values" @change="change">
                    <wd-radio v-for="item in partners" :value="item.id" shape="dot">{{ item.label }}</wd-radio>
                </wd-radio-group>
            </view>
        </scroll-view>
        <view class="p3 flex flex-justify-around bg-#ffffff">
            <wd-button type="success" customClass="w90%" @click="selectCommon">选择群组</wd-button>
        </view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { getUserTenantPageList, getAllGroupList } from '@/service/marketAction/marketAction'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast } from 'wot-design-uni'
const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const tab = ref<number>(0)

const optsData = ref([])

onLoad(async (opts) => {
    // getAll()
    console.log(opts);
    if (opts.defaultData) {
        let data = JSON.parse(opts.defaultData)
        optsData.value = data
        let data3 = partners.value.filter(aitem => data.some(bItem => bItem.id === aitem.id));
        values.value = data3.map(item => item.id)
        // console.log(commonValue.value);
    }
})
// 所有同行人搜索值
const searchValue = ref('')
// 所有同行人indexs
const values = ref<number[]>([])
// 所有同行人列表
const partners = ref([])
// 所有同行人搜索
const search = (e) => {
    console.log(e);
    getAll()
}
// 选择常用的
const selectCommon = () => {
    let data = partners.value.filter(item => item.id == values.value)
    console.log(data);
    
    router.back()
    uni.$emit('chooseGroups', data)
}

const change = (e) => {
    console.log(e);
}

// 获取所有同行人
const getAll = () => {
    // let data = {
    //     username: '',//工号
    //     realname: '',//姓名
    //     pageSize: 999,
    //     userTenantStatus: 1
    // }
    // if (searchValue.value) {
    //     data.realname = searchValue.value
    //     getUserTenantPageList(data).then((res: any) => {
    //         console.log(res);
    //         if (res.code == 200) {
    //             // res.result.records = res.result.records.filter(item => item.workNo != userInfo.workNo)
    //             partners.value = res.result.records.map(item => ({
    //                 id: item.id,
    //                 label: `${item.realname} - ${item.workNo}`,
    //                 workNo: item.workNo,
    //                 name: item.realname
    //             }))
    //             // partners.value.find(item => item.workNo == userInfo.workNo).disabled = true
    //         }
    //     })
    // }


    // 群组
    getAllGroupList({
        znkqUserId: userInfo.znkqId,
        name: `*${searchValue.value}*`,
        pageSize: 9999
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            partners.value = res.result.records.map(item => ({
                id: item.id,
                label: item.name,
                name: item.name
            }))
        }
    })
}
</script>