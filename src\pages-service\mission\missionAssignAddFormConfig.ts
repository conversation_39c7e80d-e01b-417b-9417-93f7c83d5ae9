import { FormRules } from "wot-design-uni/components/wd-form/types"

import { isArray, isBoolean, isFunction, isNumber, isString } from '@/utils/is';

const formConfig = ref([
    {
        title: '',
        form: [
            {
                type: 'text',
                label: '创建人',
                name: 'custom_createBy'
            },
            {
                type: 'dateTimePicker',
                label: '创建日期',
                name: 'taskTime',
                pickerType: 'year-month',
                displayFormat: (items) => {
                    return `${items[0].label}年${items[1].label}月`
                }
            },
            // {
            //     type: 'calendar',
            //     label: '测试日期',
            //     name: 'testTime',
            //     pickerType: 'datetimerange'
            // },
            {
                type: 'picker',
                label: '群组',
                name: 'custom_group',
                columns: [],
                placeholder: '请选择群组'
            },
            {
                type: 'text',
                label: '群组ID',
                name: 'groupId'
            },
            {
                type: 'input',
                textType: 'number',
                label: '首次拜访',
                labelWidth: '130px',
                name: 'firstVisitNum',
                placeholder: '请输入首次拜访次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '未成交重复拜访',
                labelWidth: '130px',
                name: 'undealVisitNum',
                placeholder: '请输入未成交重复拜访次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '已成交客户回访',
                labelWidth: '130px',
                name: 'dealVisitNum',
                placeholder: '请输入已成交客户回访次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '生测实验',
                labelWidth: '130px',
                name: 'biologicalTestsNum',
                placeholder: '请输入生测实验次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '示范田工作',
                labelWidth: '130px',
                name: 'modelFieldNum',
                placeholder: '请输入示范田工作次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '订货会',
                labelWidth: '130px',
                name: 'orderMeetingNum',
                placeholder: '请输入订货会次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '观摩会',
                labelWidth: '130px',
                name: 'observationMeetingNum',
                placeholder: '请输入观摩会次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '工作笔记记录',
                labelWidth: '130px',
                name: 'noteNum',
                placeholder: '请输入工作笔记记录次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '拉练',
                labelWidth: '130px',
                name: 'pullTrainingNum',
                placeholder: '请输入拉练次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '站点促销',
                labelWidth: '130px',
                name: 'promotionalNum',
                placeholder: '请输入拉练次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '产品宣传',
                labelWidth: '130px',
                name: 'productPromotionNum',
                placeholder: '请输入拉练次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '培训',
                labelWidth: '130px',
                name: 'trainingNum',
                placeholder: '请输入拉练次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '例会',
                labelWidth: '130px',
                name: 'regularMeetingNum',
                placeholder: '请输入拉练次数'
            },
            {
                type: 'input',
                textType: 'number',
                label: '农民会',
                labelWidth: '130px',
                name: 'farmersWillNum',
                placeholder: '请输入拉练次数'
            },
        ]
    }
])

const rules: FormRules = {
    custom_group: [
        {
            required: true,
            validator: (value) => {
                if (!value) {
                    return false
                } else if (isArray(value) && value.length == 0) {
                    return false
                } else {
                    return true;
                }
            },
            message: '请选择群组',
            trigger: ['change', 'blur'],
        }
    ],
    taskTime: [
        {
            required: true,
            message: '请选择时间',
            trigger: ['blur', 'change']
        }
    ],
    groupName: [
        {
            required: true,
            message: '请选择群组',
            trigger: ['blur', 'change']
        }
    ],
    firstVisitNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    undealVisitNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    dealVisitNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    biologicalTestsNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    modelFieldNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    orderMeetingNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    observationMeetingNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    noteNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    pullTrainingNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    promotionalNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    productPromotionNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    trainingNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    regularMeetingNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ],
    farmersWillNum: [
        {
            required: true,
            message: '请填写次数',
            trigger: ['blur', 'change']
        }
    ]
}

export { formConfig, rules };