import { defineStore } from 'pinia'
import { ref } from 'vue'

const initState = {
    menus: [],
    fabs:[],
    marketActionVisitState: [],
    marketActionAuditStatus: []
}

export const useGlobalStore = defineStore(
    'global',//localStorage的key名
    () => {
        // 全局状态--------------------------------------------
        const global = ref({ ...initState })
        // 菜单信息--------------------------------------------
        const setMenuInfo = (val) => {
            global.value.menus = val
        }
        const clearMenuInfo = () => {
            global.value.menus = []
        }
        const editMenuInfo = (options) => {
            global.value.menus = { ...global.value.menus, ...options }
        }
        // 悬浮窗---------------------------------------------
        const setFabs = (val) => {
            global.value.fabs = val
        }
        const clearFabs = () => {
            global.value.fabs = []
        }
        const editFabs = (options) => {
            global.value.fabs = { ...global.value.fabs, ...options }
        }
        // 拜访记录状态
        const setMarketActionVisitState = (val) => {
            global.value.marketActionVisitState = val
        }
        const clearMarketActionVisitState = () => {
            global.value.marketActionVisitState = []
        }
        // 营销动作审核状态
        const setMarketActionAuditStatus = (val) => {
            global.value.marketActionAuditStatus = val
        }
        const clearMarketActionAuditStatus = () => {
            global.value.marketActionAuditStatus = []
        }

        return {
            global,
            setMenuInfo,
            clearMenuInfo,
            editMenuInfo,

            setFabs,
            clearFabs,
            editFabs,

            setMarketActionVisitState,
            clearMarketActionVisitState,

            setMarketActionAuditStatus,
            clearMarketActionAuditStatus
        }
    },
    {
        // 如果需要持久化就写 true, 不需要持久化就写 false（或者去掉这个配置项）
        persist: true,
    },
)
