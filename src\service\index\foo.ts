import { http } from '@/utils/http'

export interface IFooItem {
  id: string
  name: string
}



/** GET 请求 */
export const getFooAPI = (name: string) => {
  return http.get<IFooItem>('/foo', { name })
}

/** POST 请求 */
export const postFooAPI = (name: string) => {
  return http.post<IFooItem>('/foo', { name }, { name })
}


// 字典
export const getDict = (code) => {
  let dictUrl = `/sys/dict/getDictItems/${code}`
  return http.get(dictUrl, {})
};

// 刷新缓存
export const refleshCache = () => http.get("/sys/dict/refleshCache");

// 退出
export const logoutSystem = () => http.get("/sys/logout");

// 录音识别获取taskId   传formData  header：'Content-Type': 'multipart/form-data'       , {}, { 'Content-type': 'multipart/form-data' }
// export const getAudioTaskId = (params) => http.post("/jeecg-action/action/visitRecords/audio/upload/trans", params);
// 录音转写
export const getAudioTransform = (params) => http.get("/jeecg-action/action/visitRecords/audio/trans/result", params);