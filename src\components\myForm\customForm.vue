<template>
    <wd-form ref="form" :model="formData" :rules="rules">
        <wd-cell-group v-for="(group, i) in formConfig" :key="i" custom-class="mt3" :title="group.title" :border="true">

            <template v-for="(item, index) in group.form" :key="index">
                <template v-if="checkShow(item.showRules)">

                    <!-- 输入框 -->
                    <wd-input v-if="item.type == 'input'" :type="item.textType" :prop="item.name" :name="item.name"
                        :label="item.label" :label-width="item.labelWidth || labelWidth" :maxlength="50" clearable
                        v-model="formData[item.name]" :placeholder="item.placeholder"
                        @blur="setFormData($event, item.name, item.type)" :disabled="item.disabled">
                        <template #suffix v-if="item.useSuffix">
                            <!-- <view>
                                {{ item.suffixText }}
                            </view> -->
                            <wd-text>{{ item.suffixText }}</wd-text>
                        </template>
                    </wd-input>

                    <!-- 滚动选择器 -->
                    <wd-picker v-if="item.type == 'picker'" :prop="item.name" :name="item.name" :columns="item.columns"
                        :label="item.label" :label-width="labelWidth" v-model="formData[item.name]"
                        :placeholder="item.placeholder" :disabled="item.disabled"
                        @confirm="setFormData($event, item.name, item.type)" />

                    <!-- 日期时间日历筛选 -->
                    <wd-calendar v-if="item.type == 'calendar'" :label="item.label" :label-width="labelWidth"
                        :prop="item.name" :name="item.name" :type="item.pickerType" v-model="formData[item.name]"
                        :disabled="item.disabled" @confirm="setFormData($event, item.name, item.type)">
                    </wd-calendar>

                    <!-- 日期时间日历范围筛选 -->
                    <!-- <wd-calendar v-if="item.type == 'calendarRange'" :prop="item.name" :name="item.name" type="datetimerange"
                        v-model="formData[item.name]" label="" :disabled="item.disabled">
                    </wd-calendar> -->

                    <!-- 日期时间滚动选择器 -->
                    <wd-datetime-picker v-if="item.type == 'dateTimePicker'" :prop="item.name" :name="item.name"
                        v-model="formData[item.name]" :label="item.label" :label-width="labelWidth"
                        :displayFormat="item.displayFormat" :disabled="item.disabled" :minDate="item.minDate || minDate"
                        :maxDate="item.maxDate || maxDate" :type="item.pickerType" />

                    <!-- 单纯文字展示 -->
                    <wd-cell v-if="item.type == 'text'" :prop="item.name" :name="item.name" :title="item.label"
                        :value="item.text" :title-width="labelWidth">
                        <view class="text-left">{{ formData[item.name] }}</view>
                    </wd-cell>

                    <!-- 单选框 -->
                    <wd-cell v-if="item.type == 'radio'" :prop="item.name" :name="item.name" :title="item.label"
                        :title-width="labelWidth" :disabled="item.disabled">
                        <wd-radio-group v-model="formData[item.name]" inline custom-class="mb2" shape="dot">
                            <wd-radio v-for="values in item.groupData" :modelValue="values.value">{{
                                values.label }}</wd-radio>
                        </wd-radio-group>
                    </wd-cell>

                    <!-- 复选框 -->
                    <wd-cell v-if="item.type == 'checkbox'" :prop="item.name" :name="item.name" :title="item.label"
                        :title-width="labelWidth" :disabled="item.disabled">
                        <wd-checkbox-group v-model="formData[item.name]" inline custom-class="mb2">
                            <wd-checkbox v-for="values in item.groupData" :modelValue="values.value" shape="square">{{
                                values.label }}</wd-checkbox>
                        </wd-checkbox-group>
                    </wd-cell>

                    <!-- 文本域 -->
                    <wd-textarea v-if="item.type == 'textarea'" :prop="item.name" :name="item.name" :label="item.label"
                        :label-width="labelWidth" v-model="formData[item.name]" :placeholder="item.placeholder"
                        auto-height :disabled="item.disabled" />

                    <!-- 语音与文本域 -->
                    <customTextAreaWithSound v-if="item.type == 'textareawithsound'" :prop="item.name" :name="item.name"
                        :label="item.label" :label-width="labelWidth" :disabled="item.disabled"
                        :placeholder="item.placeholder" :defaultData="formData.custom_audioDataDefaultData"
                        :defaultEditText="formData.custom_interviewDescriptionWithSound">
                    </customTextAreaWithSound>

                    <!-- 上传 image|video-->
                    <wd-cell v-if="item.type == 'upload'" :title="item.label" :title-width="'100%'" :prop="item.name">
                        <template #label>
                            <view class="mt2 mb2">
                                <wd-upload v-model:file-list="formData[item.name]" :accept="item.accept" multiple
                                    image-mode="aspectFill" :action="action" :header="uploadHeader"
                                    :formData="item.formData" :disabled="item.disabled" :before-preview="beforePreview"
                                    :before-upload="beforeUpload"></wd-upload>
                            </view>
                        </template>
                    </wd-cell>

                    <!-- 选择地图 -->
                    <customSelectActionLocation v-if="item.type == 'selectLocationByMap'" :prop="item.name"
                        :name="item.name" :label="item.label" :showCopy="item.showCopy" :text="formData[item.name]"
                        :defaultData="formData.custom_selectActionLocationDefaultData" @setFormData="customSetFormData"
                        :disabled="item.disabled">
                    </customSelectActionLocation>

                    <!-- 选择客户 -->
                    <!-- <customSelectClientInfo v-if="item.type == 'selectClientInfo'" :prop="item.name" :name="item.name"
                        :label="item.label" @setFormData="customSetFormData"
                        :defaultData="formData.custom_selectClientInfoDefaultData" :disabled="item.disabled">
                    </customSelectClientInfo> -->

                    <!-- 选择同行人 -->
                    <!-- <customSelectActionPartners v-if="item.type == 'selectPartners'" :prop="item.name" :name="item.name"
                        :label="item.label" :defaultData="formData.custom_selectPartners"
                        @setFormData="customSetFormData" :disabled="item.disabled">
                    </customSelectActionPartners> -->

                    <!-- 选择人员(全部) -->
                    <customSelectPeoples v-if="item.type == 'selectPeoples'" :prop="item.name" :name="item.name"
                        :label="item.label" :defaultData="formData.custom_selectPeoples"
                        @setFormData="customSetFormData" :disabled="item.disabled">
                    </customSelectPeoples>

                    <!-- 选择群(全部) -->
                    <customSelectGroups v-if="item.type == 'selectGroups'" :prop="item.name" :name="item.name"
                        :label="item.label" :defaultData="formData.custom_selectGroups" @setFormData="customSetFormData"
                        :disabled="item.disabled">
                    </customSelectGroups>

                    <!-- 销售战绩 -->
                    <!-- <customVisitSaleAchievements v-if="item.type == 'visitSalesAchievements'" :prop="item.name"
                        :name="item.name" :label="item.label" @setFormData="customSetFormData"
                        :defaultData="formData.custom_visitSalesAchievementsDefaultData" :disabled="item.disabled">
                    </customVisitSaleAchievements> -->

                    <!-- 选择经销商 -->
                    <CustomSelectWithPopup v-if="item.type == 'selectPopup'" :prop="item.name" :name="item.name"
                        :label="item.label" @setFormData="customSetFormData"
                        :defaultData="formData.custom_select_JXS_DefaultData" :disabled="item.disabled">
                    </CustomSelectWithPopup>

                </template>
            </template>
        </wd-cell-group>
        <view class="flex justify-around items-center pt3 pb3" v-if="couldSave">
            <wd-button custom-class="w45%" type="success" size="large" @click="handleSubmit('temp')" block>{{ btnText2
                }}</wd-button>
            <wd-button custom-class="w45%" type="primary" size="large" @click="handleSubmit" block>{{ btnText1
                }}</wd-button>
        </view>
        <view class="pt3 pb3" v-if="!couldSave && !disabledBtn">
            <wd-button type="primary" size="large" @click="handleSubmit" block>{{ btnText1 }}</wd-button>
        </view>
    </wd-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customSelectActionLocation from './customSelectActionLocation.vue'
import customSelectClientInfo from './customSelectClientInfo.vue'
import customSelectActionPartners from './customSelectActionPartners.vue'
import customTextAreaWithSound from './customTextAreaWithSound.vue'
import customVisitSaleAchievements from './customVisitSaleAchievements.vue'
import customSelectPeoples from './customSelectPeoples.vue'
import customSelectGroups from './customSelectGroups.vue'

import CustomSelectWithPopup from './customSelectWithPopup.vue'

import { isArray, isObject } from '@/utils/is'
import { http } from '@/utils/http'

// import customUpload from './customUpload.vue'

const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

const labelWidth = '80px'
const props = defineProps({
    formData: {
        type: Object,
    },
    rules: {
        type: Object
    },
    formConfig: {
        type: Array
    },
    couldSave: {
        type: Boolean,
        default: false
    },
    btnText1: {
        type: String,
    },
    btnText2: {
        type: String,
    },
    disabledBtn: {
        type: Boolean,
        default: false
    }
})


// const emit = defineEmits(['setFormData', 'customSetFormData'])
const emit = defineEmits<{
    (event: string, ...args: any[]): void
}>()

const setFormData = (e: any, name: string, type: string) => {
    // console.log(e, name, type);
    emit('setFormData', {
        data: e,
        name: name,
        type: type
    })
}
const customSetFormData = (e) => {
    console.log(e);
    emit('customSetFormData', e)
}

// 展示规则
const checkShow = (rules) => {
    if (isObject(rules)) {
        return isArray(rules.arr) && rules.arr.includes(props.formData[rules.by])
    } else if (isArray(rules)) {
        return rules.every(obj => isArray(obj.arr) && obj.arr.includes(props.formData[obj.by]))
    } else {
        return true
    }
}

const form = ref()
const handleSubmit = (flag) => {
    form.value
        .validate()
        .then(({ valid, errors }) => {
            // console.log(props.formData);
            // console.log(valid)
            if (valid) {
                if (flag == 'temp') {
                    emit('submit', 'temp')
                } else {
                    emit('submit')
                }
            }
            // console.log(errors)
        })
        .catch((error) => {
            console.log(error, 'error')
        })
}



const uploadHeader = http.uploadHeaderOptions()
const apiUrl = import.meta.env.VITE_SERVER_BASEURL
const action: string = `${apiUrl}/sys/common/upload`
// const action: string = `${apiUrl}`
const beforePreview = ({ file, index, imgList, resolve }) => {
    console.log(file);
    // H5----------------------------------------------------------------
    // H5----------------------------------------------------------------
    // H5----------------------------------------------------------------
    // #ifdef H5
    if (file.name.indexOf('.png') != -1 || file.name.indexOf('.jpg') != -1) {
        if (file.response) {
            let res = JSON.parse(file.response)
            console.log(res.message);
            uni.previewImage({
                urls: [res.message]
            })
        }
    } else {
        resolve(true)
    }
    // #endif
    // 微信----------------------------------------------------------------
    // 微信----------------------------------------------------------------
    // 微信----------------------------------------------------------------
    // #ifdef MP-WEIXIN
    if (file.url.indexOf('.png') != -1 || file.url.indexOf('.jpg') != -1) {
        if (file.response) {
            let res = JSON.parse(file.response)
            console.log(res.message);
            uni.previewImage({
                urls: [res.message]
            })
        }
    } else {
        resolve(true)
    }
    // #endif
}
// 上传前置处理
const beforeUpload = ({ files, resolve }) => {
    console.log(files);
    resolve(true)
}

const displayFormat = (items) => {
    return `${items[0].label}年${items[1].label}月${items[2].label}日 ${items[3].label}:${items[4].label}`
}

</script>

<style lang="scss" scoped></style>
