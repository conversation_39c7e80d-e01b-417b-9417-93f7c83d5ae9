<template>
  <view class="content">
	  <echartsUniapp :option="option"></echartsUniapp>
  </view>
</template>

<script lang="ts" setup>
import { echartProps } from '@/pages-work/components/echarts/props';
import echartsUniapp from '@/pages-work/components/echarts/index.vue';
//组件传参
// const props = defineProps({
// 	...echartProps
// })
//最终图表配置项
const option = ref({
    title: {
        text: 'ECharts 入门示例'
    },
    tooltip: {},
    legend: {
        data: ['销量']
    },
    xAxis: {
        data: ["衬衫","羊毛衫","雪纺衫","裤子","高跟鞋","袜子"]
    },
    yAxis: {},
    series: [
        {
            name: '销量',
            type: 'bar',
            data: [5, 20, 36, 10, 10, 20]
        }
    ]
});

onMounted(()=>{
	// queryData();
})

</script>
<style>
.content {
  padding: 10px;
}
</style>
