<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '地图',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout :navTitle="customTitle" :showFab="false">
        <scroll-view scroll-y>
            <customForm :formConfig="formConfig" :formData="formData" :rules="rules" @setFormData="setFormData"
                @customSetFormData="customSetFormData" @submit="submit" :couldSave="couldSave" btnText1="最终提交"
                btnText2="临时保存">
            </customForm>
        </scroll-view>
        <wd-message-box></wd-message-box>
        <wd-popup v-model="showSaveSuccess" custom-style="border-radius:32rpx;" @close="showSaveSuccess = false"
             :close-on-click-modal="false">
            <view class="flex flex-col items-center pt8 pl4 pr4 pb6 rd-8">
                <view>操作成功!</view>
                <wd-button type="success" custom-class="mt4"
                    @click="toNext('/pages-service/marketAction/marketActionAddRecordMap')">去轨迹地图</wd-button>
                <wd-button type="success" custom-class="mt4"
                    @click="toNext('/pages-service/marketAction/marketActionActionMap')">去活动地图</wd-button>
                <wd-button type="success" custom-class="mt4"
                    @click="toNext('/pages-service/marketAction/marketActionSelfQuery')">去我的列表</wd-button>
            </view>
        </wd-popup>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
// import customForm from '@/pages-service/myForm/customForm.vue'
import { formConfig, rules } from './hdswFormConfig'
import { addMarketActionRecord, editMarketActionRecord, finalSubmission } from '@/service/marketAction/marketAction'
import { getDict } from '@/service/index/foo'

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

const thisAction = ref('')
const customTitle = computed(() => {
    if (thisAction.value == 'add') {
        return '添加记录'
    } else if (thisAction.value == 'edit') {
        return '修改记录'
    }
})

const couldSave = ref(true)

const message = useMessage()

const toast = useToast()

const showSaveSuccess = ref(false)

let formData = reactive<any>({
    startTime: 0,
    endTime: 0,
    stayTime: '',
    address: '',
    visitType: ''
})

// formData = {...formData,...{age:123}}

// 监听开始时间
watch(
    () => formData.startTime,
    (n, o) => {
        let num = formData.endTime - formData.startTime
        // console.log(num);
        formData.stayTime = gethms(num)
    }
)
// 监听结束时间
watch(
    () => formData.endTime,
    (n, o) => {
        let num = formData.endTime - formData.startTime
        // console.log(num);
        formData.stayTime = gethms(num)
    }
)

// 表单值改变后可进行其他操作--------------------------
const setFormData = (e) => {
    console.log(e);
}

const customSetFormData = (e) => {
    console.log(e);
}

// 提交
const submit = (temp) => {
    console.log(formData);
    return
    //判断停留时间
    if (formData.stayTime.indexOf('-') != -1) {
        toast.warning('停留时间不能为负')
        return
    }
    // 删除key的开头为custom_的值----------------
    // Object.keys(formData).forEach(key => {
    //     if (key.startsWith('custom_')) {
    //         delete formData[key];
    //     }
    // });
    // 处理图片和视频url
    const apiUrl = import.meta.env.VITE_SERVER_BASEURL
    // const prefixUrl = `${apiUrl}/sys/common/static/`
    const prefixUrl = ``

    // console.log(formData.origin_clientVideo);
    // return

    if (thisAction.value == 'add') {
        if (formData.origin_clientGatePics) {
            formData.origin_clientGatePics = formData.origin_clientGatePics.filter(item => item.status == 'success')
            formData.clientGatePics = JSON.stringify(formData.origin_clientGatePics.map(item => `${prefixUrl}${JSON.parse(item.response).message}`))
        }
        if (formData.origin_wechatScreenshotPics) {
            formData.origin_wechatScreenshotPics = formData.origin_wechatScreenshotPics.filter(item => item.status == 'success')
            formData.wechatScreenshotPics = JSON.stringify(formData.origin_wechatScreenshotPics.map(item => `${prefixUrl}${JSON.parse(item.response).message}`))
        }
        if (formData.origin_clientVideo) {
            formData.origin_clientVideo = formData.origin_clientVideo.filter(item => item.status == 'success')
            formData.clientVideo = JSON.stringify(formData.origin_clientVideo.map(item => `${prefixUrl}${JSON.parse(item.response)?.message.split('?')[0]}`))
        }
    } else if (thisAction.value == 'edit') {
        if (formData.origin_clientGatePics) {
            let arr = []
            for (const i in formData.origin_clientGatePics) {
                if(formData.origin_clientGatePics[i].response){
                    arr.push(`${prefixUrl}${JSON.parse(formData.origin_clientGatePics[i].response).message}`)
                }else{
                    arr.push(formData.origin_clientGatePics[i].url)
                }
            }
            formData.clientGatePics = JSON.stringify(arr)
        }
        if (formData.origin_wechatScreenshotPics) {
            let arr = []
            for (const i in formData.origin_wechatScreenshotPics) {
                if(formData.origin_wechatScreenshotPics[i].response){
                    arr.push(`${prefixUrl}${JSON.parse(formData.origin_wechatScreenshotPics[i].response).message}`)
                }else{
                    arr.push(formData.origin_wechatScreenshotPics[i].url)
                }
            }
            formData.wechatScreenshotPics = JSON.stringify(arr)
        }
        if (formData.origin_clientVideo) {
            let arr = []
            for (const i in formData.origin_clientVideo) {
                if(formData.origin_clientVideo[i].response){
                    arr.push(`${prefixUrl}${JSON.parse(formData.origin_clientVideo[i].response)?.message.split('?')[0]}`)
                }else{
                    arr.push(formData.origin_clientVideo[i].url)
                }
            }
            formData.clientVideo = JSON.stringify(arr)
        }
    }

    // 处理visitTypeInfo信息
    formData.visitTypeInfo = {
        joinPersonNum: formData.custom_joinPersonNum || '',
        productName: formData.custom_productName || '',
        productNum: formData.custom_productNum || '',
        regularMeetingContent: formData.custom_regularMeetingContent || '',
        regularMeetingHost: formData.custom_regularMeetingHost || '',
        regularMeetingJoin: formData.custom_regularMeetingJoin || '',
        regularMeetingLeaveNum: formData.custom_regularMeetingLeaveNum || '',
        regularMeetingReachNum: formData.custom_regularMeetingReachNum || '',
        regularMeetingTime: formData.custom_regularMeetingTime || '',
        regularMeetingType: formData.custom_regularMeetingType || '',
        trainTheme: formData.custom_trainTheme || '',
        trainType: formData.custom_trainType || '',
        workContent: formData.custom_workContent || '',
    }

    // 处理同行人visitRecordsPartners
    if (formData.custom_selectPartners) {
        formData.visitRecordsPartners = formData.custom_selectPartners.map(item => ({
            userAccount: item.workNo,
            userId: item.id,
            userName: item.name,
        }))
    }

    // console.log(formData.visitRecordsPartners);

    // console.log(userInfo);
    // return
    
    let user = {
        userId: userInfo.id,
        userName: userInfo.realname,
        state: 1,
    }
    if (temp) {
        user.state = 0
    }
    let data = { ...formData, ...user }
    console.log('合并数据', data);
    // 请求添加
    if (thisAction.value == 'add') {
        addMarketActionRecord(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                showActionSuccess()
            } else {
                toast.error(res.message)
            }
        })
    } else if (thisAction.value == 'edit') {
        if (temp) {
            editMarketActionRecord(data).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    showActionSuccess()
                } else {
                    toast.error(res.message)
                }
            })
        } else {
            finalSubmission(data).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    showActionSuccess()
                } else {
                    toast.error(res.message)
                }
            })
        }
    }
}

const showActionSuccess = () => {
    showSaveSuccess.value = true
}

const toNext = (url) => {
    router.replace({
        path: url,
        query: {
            date: formData.startTime
        }
    })
}

const setDictColumns = (dictText, ziduan) => {
    getDict(dictText).then((res: any) => {
        let FORM = formConfig.value[0].form
        for (const i in FORM) {
            if (FORM[i].name == ziduan) {
                FORM[i].columns = res.result.map(item => ({
                    value: Number(item.value),
                    label: item.label
                }))
            }
        }
        // formConfig.value[0].form.find(item => item.name == ziduan).columns = res.result.map(item => ({
        //     value: Number(item.value),
        //     label: item.label
        // }))
    })
}

onLoad((opts) => {
    console.log('onload', opts);
    
    thisAction.value = opts.action

    // // 工作类型
    // setDictColumns('visitType', 'visitType')
    // // 生测实验/示范田类型
    // setDictColumns('thisWorkType', 'thisWorkType')
    // // 产品宣传（工作内容）
    // setDictColumns('productContent', 'custom_workContent')
    // // 培训形式
    // setDictColumns('trainType', 'custom_trainType')
    // // 例会类型
    // setDictColumns('regularMeetingType', 'custom_regularMeetingType')


    // 设置图片地址水印
    setPicMarkAddress(opts.address)

    // console.log(formConfig.value[0].form);

    // 添加操作
    if (opts.action == 'add') {
        formConfig.value[0].form = formConfig.value[0].form.map(item => ({
            ...item,
            disabled: false
        }))
        // 停留时长
        formData.stayTime = Number(opts.info0endTime) - Number(opts.info0startTime)
        // 开始结束时间
        formData.startTime = Number(opts.info0startTime)
        formData.endTime = Number(opts.info0endTime)
        // 地址
        formData.address = opts.address
        // 地址经纬度(-)
        formData.addressLng = opts.lng
        formData.addressLat = opts.lat
        // 选择位置默认的地址(-)
        formData.custom_selectActionLocationDefaultData = {
            address: opts.address,
            lnglat: opts.location
        }
        // 重置语音链接
        formData.custom_audioDataDefaultData = []
    }
    // 编辑操作
    if (opts.action == 'edit' && opts.editData) {
        let data = JSON.parse(decodeURIComponent(opts.editData))
        console.log(data);
        if (data.state == 1) {
            // 设置禁用
            couldSave.value = false
            formConfig.value[0].form = formConfig.value[0].form.map(item => ({
                ...item,
                disabled: true
            }))
            console.log(formConfig.value);
            let fff = formConfig.value[0].form
            for (const i in fff) {
                if (fff[i].name == 'interviewDescription') {
                    fff[i].disabled = false
                }
            }
            // formConfig.value[0].form.find(item => item.name == 'interviewDescription').disabled = false
        } else {
            formConfig.value[0].form = formConfig.value[0].form.map(item => ({
                ...item,
                disabled: false
            }))
        }
        formData.id = data.id
        // 类型时间地点
        formData.visitType = data.visitType
        formData.startTime = new Date(data.startTime).getTime()
        formData.endTime = new Date(data.endTime).getTime()
        formData.stayTime = formData.endTime - formData.startTime
        formData.address = data.address
        formData.addressLng = data.clientGateLng
        formData.addressLat = data.clientGateLat
        formData.custom_selectActionLocationDefaultData = {
            address: data.address,
            lnglat: `${data.clientGateLng},${data.clientGateLat}`
            // 经纬度
        }
        // 客户信息
        formData.clientGateAddress = data.clientGateAddress
        formData.clientGateLat = data.clientGateLat
        formData.clientGateLng = data.clientGateLng
        formData.clientName = data.clientName
        formData.clientPhone = data.clientPhone
        formData.custom_selectClientInfoDefaultData = {
            clientName: data.clientName,
            clientPhone: data.clientPhone
        }
        // 描述
        formData.interviewDescription = data.interviewDescription
        // 图片视频
        data.clientGatePics ? formData.origin_clientGatePics = data.clientGatePics : ''
        data.wechatScreenshotPics ? formData.origin_wechatScreenshotPics = data.wechatScreenshotPics : ''
        data.clientVideo ? formData.origin_clientVideo = data.clientVideo : ''
        // 重置语音链接
        formData.custom_audioDataDefaultData = []
        // 录音
        if (data.audioData) {
            try {
                let audioData = JSON.parse(data.audioData)
                formData.custom_audioDataDefaultData = audioData
            } catch (error) {
                console.log(error);
            }
        }
        // 录音回显文字
        formData.custom_interviewDescriptionWithSound = data.interviewDescription
        // 生测示范田操作
        formData.modelNum = data.modelNum
        formData.thisWorkType = data.thisWorkType
        // 拉练
        formData.bannerNum = data.bannerNum
        formData.picturePosterNum = data.picturePosterNum
        formData.leafletNum = data.leafletNum
        formData.storeSalesVolume = data.storeSalesVolume
        formData.dosageDesc = data.dosageDesc
        formData.sprayDesc = data.sprayDesc
        formData.patrolDesc = data.patrolDesc
        formData.issuedPlanDesc = data.issuedPlanDesc
        // 站点促销回显
        formData.visitSalesAchievements = data.visitSalesAchievements
        formData.custom_visitSalesAchievementsDefaultData = data.visitSalesAchievements
        // 处理visitTypeInfo信息
        formData.custom_joinPersonNum = data?.visitTypeInfo?.joinPersonNum || ''
        formData.custom_productName = data?.visitTypeInfo?.productName || ''
        formData.custom_productNum = data?.visitTypeInfo?.productNum || ''
        formData.custom_regularMeetingContent = data?.visitTypeInfo?.regularMeetingContent || ''
        formData.custom_regularMeetingHost = data?.visitTypeInfo?.regularMeetingHost || ''
        formData.custom_regularMeetingJoin = data?.visitTypeInfo?.regularMeetingJoin || ''
        formData.custom_regularMeetingLeaveNum = data?.visitTypeInfo?.regularMeetingLeaveNum || ''
        formData.custom_regularMeetingReachNum = data?.visitTypeInfo?.regularMeetingReachNum || ''
        formData.custom_regularMeetingTime = data?.visitTypeInfo?.regularMeetingTime || ''
        formData.custom_regularMeetingType = data?.visitTypeInfo?.regularMeetingType || ''
        formData.custom_trainTheme = data?.visitTypeInfo?.trainTheme || ''
        formData.custom_trainType = data?.visitTypeInfo?.trainType || ''
        formData.custom_workContent = data?.visitTypeInfo?.workContent || ''
        // 同行人
        if (opts.editDataPartners) {
            let p = JSON.parse(decodeURIComponent(opts.editDataPartners))
            // console.log('同行',p);
            formData.custom_selectPartners = p.map(item => ({
                id: item.userId,
                label: `${item.userName} - ${item.userAccount}`,
                name: item.userName,
                workNo: item.userAccount
            }))
        }
        console.log('onloadfinalformdata', formData);

    }
    // --------------------------------
    // 选择地址
    uni.$on('chooseLocation', (e) => {
        console.log(e);
        if (e.name.value == "address") {
            formData.addressLng = e.lnglat[0]
            formData.addressLat = e.lnglat[1]
            formData.address = e.address
            console.log(formData);
            setPicMarkAddress(e.address)
        } else if (e.name.value == 'clientGateAddress') {
            formData.clientGateAddress = e.address
            formData.clientGateLng = e.lnglat[0]
            formData.clientGateLat = e.lnglat[1]
        }
    })
    // 复制地址
    uni.$on('copyLocation', (e) => {
        formData.clientGateLng = formData.addressLng
        formData.clientGateLat = formData.addressLat
        formData.clientGateAddress = formData.address
    })
    // // 客户
    // uni.$on('chooseClient', (e) => {
    //     console.log(e);
    //     formData.clientName = e.clientName
    //     formData.clientPhone = e.clientPhone
    //     formData.custom_selectClientInfoDefaultData = {
    //         clientName: e.clientName,
    //         clientPhone: e.clientPhone
    //     }
    // })
    // // 同行人
    // uni.$on('choosePartners', (e) => {
    //     console.log(e);
    //     formData.custom_selectPartners = e
    // })
    // // 删除同行人
    // uni.$on('deletePartners', (e) => {
    //     // console.log(e);
    //     formData.custom_selectPartners = formData.custom_selectPartners.filter(item => item.id != e.id)
    // })
    // // 设置语音链接
    // uni.$on('setAudioUrls', (e) => {
    //     console.log(e);
    //     let data = e.param
    //     formData.audioData = JSON.stringify(data)
    //     formData.custom_audioDataDefaultData = data
    // })
    // // 设置语音描述
    // uni.$on('setRemark', (e) => {
    //     // console.log(e);
    //     let data = e.param
    //     formData[e.name] = data
    // })
    // // 销售战绩
    // uni.$on('setSaleAchievements', (e) => {
    //     console.log(e);
    //     formData.visitSalesAchievements = e
    // })
})

onUnload(() => {
    uni.$off('chooseLocation')
    uni.$off('copyLocation')
    uni.$off('chooseClient')
    uni.$off('choosePartners')
    uni.$off('deletePartners')
    uni.$off('setAudioUrls')
    uni.$off('setRemark')
    uni.$off('setSaleAchievements')
})

// 设置图片水印地址
const setPicMarkAddress = (address) => {
    let config = formConfig.value[0].form
    for (const c in config) {
        // 图片上传要传地址
        if (config[c].name == 'origin_clientGatePics') {
            config[c].formData = {
                address: address
            }
        }
        if (config[c].name == 'origin_wechatScreenshotPics') {
            config[c].formData = {
                address: address
            }
        }
    }
}

// 获取单时分秒
const gethms = (timestamp) => {
    // 将时间戳转换为毫秒数
    const milliseconds = parseInt(timestamp, 10);
    // 计算总分钟数
    const totalMinutes = Math.floor(milliseconds / 60000);
    // 计算小时数和剩余的分钟数
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const seconds = minutes % 60;
    // 返回格式化的字符串
    return `${hours}小时${minutes}分钟${seconds}秒`;
}
</script>

<style lang="scss" scoped></style>