<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '添加群的督查授权',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout :navTitle="customTitle" :showFab="false">
        <scroll-view scroll-y>
            <customForm :formConfig="formConfig" :formData="formData" :rules="rules" @setFormData="setFormData"
                @customSetFormData="customSetFormData" @submit="submit" btnText1="提交" :disabledBtn="action == 'check'">
            </customForm>
        </scroll-view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './missionGroupAuthAddFormConfig'
import { addMissionGroupAuth, getMissionGroupAuthListById, editMissionGroupAuth, getMyCreateGroupList, getUserGroupList } from '@/service/marketAction/marketAction'

const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const action = ref('')
const customTitle = computed(() => {
    if (action.value == 'add') {
        return '添加群的督查授权'
    } else if (action.value == 'edit') {
        return '修改群的督查授权'
    } else if (action.value == 'check') {
        return '查看群的督查授权'
    }
})

const formData = ref({
    custom_createBy: userInfo.realname,
    custom_createTime: dayjs(Date.now()).format('YYYY-MM-DD'),
    // groupId:,
})

// 原组件表单值改变后可进行其他操作--------------------------
const setFormData = (e) => {
    console.log(e);
    if (e.name == 'custom_groupName') {
        formData.value.groupId = e.data.selectedItems.id
        formData.value.groupName = e.data.selectedItems.name
        formData.value.groupOwnerId = e.data.selectedItems.groupOwnerId
        formData.value.groupOwnerName = e.data.selectedItems.groupOwnerName
        formData.value.groupOwnerAccount = e.data.selectedItems.groupOwnerAccount

        getUserGroupList({
            groupId: e.data.selectedItems.id
        }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                let data = res.result.map(item => ({
                    ...item,
                    value: item.userId,
                    label: item.userName
                }))
                formConfig.value[0].form[6].columns = ref(data)
            }
        })
    }
    if (e.name == 'custom_groupManageName') {
        console.log(formData.value.custom_groupManageName);
        
        formData.value.groupManageId = e.data.selectedItems.userId
        formData.value.groupManageName = e.data.selectedItems.userName
        formData.value.groupManageAccount = e.data.selectedItems.userAccount
    }

}
// 自定义组件表单值改变后可进行其他操作--------------------------
const customSetFormData = (e) => {
    console.log(e);
}

onLoad((opts) => {
    console.log(opts);
    action.value = opts.action

    // 查询群组
    getMyCreateGroupList({
        znkqUserId: userInfo.znkqId
    }).then((res: any) => {
        if (res.code == 200) {
            formConfig.value[0].form[2].columns = res.result.map(item => ({
                ...item,
                value: Number(item.id),
                label: item.name
            }))
        }
    })

    if (opts.action == 'edit') {
        getMissionGroupAuthListById({ id: opts.id }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                getUserGroupList({
                    groupId: res.result.groupId
                }).then((RES: any) => {
                    console.log(RES);
                    if (RES.code == 200) {
                        let data = RES.result.map(item => ({
                            ...item,
                            value: item.userId,
                            label: item.userName
                        }))
                        formConfig.value[0].form[6].columns = ref(data)
                    }
                })
                res.result.custom_createBy = res.result.createBy
                res.result.custom_createTime = res.result.createTime
                res.result.custom_groupName = res.result.groupId
                res.result.custom_groupManageName = Number(res.result.groupManageId)
                formData.value = res.result
            }
        })
        setFormDisAbled(false)
    }
    if (opts.action == 'check') {
        getMissionGroupAuthListById({ id: opts.id }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                getUserGroupList({
                    groupId: res.result.groupId
                }).then((RES: any) => {
                    console.log(RES);
                    if (RES.code == 200) {
                        let data = RES.result.map(item => ({
                            ...item,
                            value: item.userId,
                            label: item.userName
                        }))
                        formConfig.value[0].form[6].columns = ref(data)
                    }
                })
                res.result.custom_createBy = res.result.createBy
                res.result.custom_createTime = res.result.createTime
                res.result.custom_groupName = res.result.groupId
                res.result.custom_groupManageName = Number(res.result.groupManageId)
                formData.value = res.result
            }
        })
        setFormDisAbled(true)
        console.log(formConfig.value);
    } else {
        formConfig.value[0].form[6].columns = ref([])
        setFormDisAbled(false)
    }
})

const setFormDisAbled = (flag) => {
    formConfig.value[0].form = formConfig.value[0].form.map(item => ({
        ...item,
        disabled: flag
    }))
}

const submit = () => {
    if (action.value == 'add') {
        let data = JSON.parse(JSON.stringify(formData.value))
        console.log(data);

        addMissionGroupAuth(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '添加成功！')
            } else {
                toast.error(res.message)
            }
        })
    }

    if (action.value == 'edit') {
        let data = formData.value
        editMissionGroupAuth(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '编辑成功！')
            } else {
                toast.error(res.message)
            }
        })
    }
}

</script>

<style lang="scss" scoped></style>