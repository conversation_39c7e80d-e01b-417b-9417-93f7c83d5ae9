`drag` 是 `uni-ui`提供的一套仪表盘组件 ，通过一些配置项和数据，实现简单的仪表盘操作，比如柱形图、饼图等。

### [查看文档](https://uniapp.dcloud.io/component/uniui/uni-sass)
#### 如使用过程中有任何问题，或者您对jeecg-boot-uniapp有一些好的建议，欢迎加入 jeecg-boot 交流群：816531124
目录结构：
├─drag 仪表盘主包
│  ├─button 自定义按钮组件
│  ├─carousel 轮播图组件
│  ├─editor 富文本组件
│  ├─iframe iframe组件
│  ├─number 数值组件
│  ├─uni-popup popup弹窗组件
│  ├─Empty.vue 为空时展示组件 
│  ├─echarts echart组件
│  │   ├─DoubleLineBar 双轴图
│  │   ├─JBar 柱形图
│  │   ├─JBubble 气泡图
│  │   ├─JCircleRadar 圆形雷达图
│  │   ├─JColorGauge 颜色仪表盘
│  │   ├─JFunnel 漏斗图
│  │   ├─JGauge 仪表盘
│  │   ├─JLine  折线图
│  │   ├─JMUltipleBar 多柱形图
│  │   ├─JMUltipleLine 多折线图
│  │   ├─JNegativeBar 正负条形图
│  │   ├─JPie 饼图
│  │   ├─JPyramid 金字塔图
│  │   ├─JRadar 雷达图
│  │   ├─JRing 环形图
│  │   ├─JScatter 散点图
│  │   ├─JStackBar 堆叠图
│  │   ├─Map 地图
│  │   │  ├─BarMap 柱形地图   
│  │   │  ├─BubbleMap 气泡地图 
│  │   │  ├─HeatMap 热力地图 
│  │   │  ├─data 地图数据 
│  │   │  ├─index.vue 地图基础组件 
│  │   ├─mixins echart混入
│  │   ├─utils 工具类
│  │   ├─index.vue echart基础组件 
│  │   │ 
└────────props.js 组件参数
