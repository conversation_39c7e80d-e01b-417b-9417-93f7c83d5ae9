import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate' // 数据持久化

// pinia-plugin-persistedstate 提供了修改 storage 存储 API 的方式（默认是 localStorage，是一个 WEB API，非H5端 不支持）
const store = createPinia()
store.use(
  createPersistedState({
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
  }),
)

export default store

// 模块统一导出
export * from './user'
export * from './global'