<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '选择位置',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout navTitle="选择位置" :showFab="false">
        <!-- #ifdef H5 -->
        <view id="container" class="w100% h75vh"></view>
        <view class="bg-#ffffff p3 w100%">
            <view class="font-size-4 w95%">
                <wd-text :text="addressText" bold></wd-text>
            </view>
            <wd-button custom-class="mt2 w95%" @click="selAdd">选择位置</wd-button>
        </view>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <map class="w100% h64vh" :latitude="latitudeWX" :longitude="longitudeWX" :markers="markersWX" @tap="mapTap"
            :scale="scaleWX" @poitap="poiTap" @markertap.prevent="markerTapWX">
            <cover-view class="absolute right-4 bottom-9 bg-#ffffff rd-2">
                <cover-view class="p2 text-center font-size-6" @click="getLocationWX">👤</cover-view>
            </cover-view>
        </map>
        <view class="bg-#ffffff px3 pb3 ml2 mt2 w90% rd-3">
            <view>
                <wd-search v-model="searchTextWX" @search="searchLocation" hide-cancel placeholder="搜索地点" />
            </view>
            <view class="font-size-4">
                <wd-text :text="addressText" bold></wd-text>
            </view>
            <wd-button custom-class="mt2 w100%" @click="selAddWX">选择位置</wd-button>
        </view>
        <wd-popup v-model="showPopWX" custom-style="border-radius:32rpx;" @close="showPopWX = false">
            <view class="w80vw h60vh p2">
                <wd-cell-group :border="true">
                    <wd-cell :title="item.addr" :value="item.title" v-for="(item, index) in LocationsWX" clickable
                        @click="toLocationWX(item)" />
                </wd-cell-group>
            </view>
        </wd-popup>
        <!-- #endif -->
    </PageLayout>
</template>

<script lang="ts" setup>

import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import AMapLoader from '@amap/amap-jsapi-loader';
import { useRouter } from '@/plugin/uni-mini-router';
import md5 from 'md5'
import QQMapWX from '@/utils/qqmap-wx-jssdk.min'
import { wgs84_to_gcj02 } from '@/utils/transformlocation'
import { throttle } from 'wot-design-uni/components/common/util';

const qqmapsdk = new QQMapWX({
    key: '6UXBZ-6CNKT-L2VXN-VLZKP-YD6H2-Q5FPS' // 替换为你的腾讯地图密钥
});

const router = useRouter()

const name = ref('')
const addressText = ref('')

const searchTextWX = ref('')






















// #ifdef MP-WEIXIN
const scaleWX = ref(13)
const latitudeWX = ref(22.833900)
const longitudeWX = ref(108.313000)
const markersWX = ref([])
const showPopWX = ref(false)
const LocationsWX = ref([])


const mapTap = (e) => {
    console.log('maptap', e);
    getAddress(e.detail.latitude, e.detail.longitude)
}

const poiTap = (e) => {
    console.log('poitap', e);
    getAddress(e.detail.latitude, e.detail.longitude)
}

const markerTapWX = (e) => {
    // console.log('markertap', e);
    // let m = markersWX.value.find(item => item.id == e.markerId)
    // getAddress(m.latitude, m.longitude)
}

// 调用腾讯地图逆地址解析接口
const getAddress = (lat, lng) => {
    // 计算签名（sig）
    // const sig = calculateSig(lat, lng);

    qqmapsdk.reverseGeocoder({
        location: {
            latitude: lat,
            longitude: lng
        },
        // sig: sig, // 签名参数
        success: (res) => {
            console.log(res);
            // 地址
            addressText.value = `${res.result.address_component.province}${res.result.address_component.city}
            ${res.result.formatted_addresses.recommend}`
            // const address = res.result.formatted_addresses.recommend; // 推荐地址描述
            markersWX.value = []
            let marker = {
                id: 0,
                longitude: lng,
                latitude: lat,
                width: 60,
                height: 60,
                iconPath: '/static/location.png',
            }
            markersWX.value.push(marker)
        },
        fail: (err) => {
            console.error('逆地址解析失败：', err);
            // that.setData({ address: '获取地址失败' });
        }
    });


}

const setAddress = (e) => {
    addressText.value = `${e.addr}${e.title}`
    longitudeWX.value = e.longitude
    latitudeWX.value = e.latitude
    markersWX.value = []
    let marker = {
        id: 0,
        longitude: e.longitude,
        latitude: e.latitude,
        width: 60,
        height: 60,
        iconPath: '/static/location.png',
    }
    markersWX.value.push(marker)
    showPopWX.value = false
}


// // 计算签名（sig）方法
// const calculateSig = (lat, lng) => {
//     const key = '6UXBZ-6CNKT-L2VXN-VLZKP-YD6H2-Q5FPS'; // 替换为你的腾讯地图密钥
//     const skuid = ''; // 替换为你的Secret Key（在腾讯地图控制台获取）
//     const str = `/ws/geocoder/v1?key=${key}&location=${lat},${lng}`;
//     return md5(str + skuid); // MD5加密（需引入MD5库）
// }

onLoad(async (opts) => {
    console.log(opts);

    name.value = opts.name
    addressText.value = opts.address
    let ll = null
    if (opts.lnglat) {
        ll = opts?.lnglat.split(',')
        longitudeWX.value = ll[0]
        latitudeWX.value = ll[1]

        let marker = {
            id: 0,
            longitude: longitudeWX.value,
            latitude: latitudeWX.value,
            width: 60,
            height: 60,
            iconPath: '/static/location.png',
        }
        markersWX.value.push(marker)
    }
})

const selAddWX = () => {
    router.back()
    uni.$emit('chooseLocation', {
        name: name,
        address: addressText.value,
        lnglat: [longitudeWX.value, latitudeWX.value]
    })
}

// 获取定位权限---------------------------------------
const getLocationWX = () => {
    uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
            setMyCenter()
        },
        fail: (err) => {
            uni.showModal({
                title: '提示',
                content: '若不授权位置，将无法定位自身位置',
                cancelText: '不授权',
                cancelColor: '#999',
                confirmText: '授权',
                confirmColor: '#f94218',
                success(res) {
                    console.log(res);
                    if (res.confirm) {
                        // 选择弹框内授权
                        uni.openSetting({
                            success(res) {
                                setMyCenter()
                                console.log(res.authSetting);
                            }
                        });
                    } else if (res.cancel) {
                        // 选择弹框内 不授权
                        uni.showToast({
                            title: '定位未授权将无法定位到自身位置',
                            icon: 'none'
                        });
                    }
                }
            });
        }
    })
}


// 设置中心点
const setMyCenter = () => {
    uni.getLocation({
        type: 'wgs84',
        success: res => {
            console.log(res);
            let trans = wgs84_to_gcj02(res.longitude, res.latitude)
            console.log(trans);

            longitudeWX.value = trans.longitude
            latitudeWX.value = trans.latitude

            getAddress(latitudeWX.value, longitudeWX.value)
        },
        fail: err => {
            console.log(err);

        }
    });
}

//搜索位置
const searchLocation = (e) => {
    console.log(e);

    //调用关键词提示接口
    qqmapsdk.getSuggestion({
        //获取输入框值并设置keyword参数
        keyword: e.value, //用户输入的关键词，可设置固定值,如keyword:'KFC'
        page_size: 20,
        //region:'北京', //设置城市名，限制关键词所示的地域范围，非必填参数
        success: function (res) {//搜索成功后的回调
            console.log(res);
            var sug = [];
            for (var i = 0; i < res.data.length; i++) {
                sug.push({ // 获取返回结果，放到sug数组中
                    title: res.data[i].title,
                    id: res.data[i].id,
                    addr: res.data[i].address,
                    city: res.data[i].city,
                    district: res.data[i].district,
                    latitude: res.data[i].location.lat,
                    longitude: res.data[i].location.lng
                });
            }
            LocationsWX.value = sug
            showPopWX.value = true
            // _this.setData({ //设置suggestion属性，将关键词搜索结果以列表形式展示
            //   suggestion: sug,
            //   showSuggestion: true,
            // });
        },
        fail: function (error) {
            console.error(error);
        },
        complete: function (res) {
            console.log(res);
        }
    });
}

const toLocationWX = (item) => {
    console.log(item);
    setAddress(item)
}


// 搜索地图poi
const searchMapWX = () => {
    qqmapsdk.search({
        keyword: searchTextWX.value,  //搜索关键词
        //   location: '39.980014,116.313972',  //设置周边搜索中心点
        success: function (res) { //搜索成功后的回调

            console.log(res);

            if (res.data.length == 0) {
                uni.showToast({
                    title: '未找到',
                    icon: 'none'
                })
                return
            }

            // var mks = []
            for (var i = 0; i < res.data.length; i++) {
                markersWX.value.push({ // 获取返回结果，放到mks数组中
                    // title: res.data[i].title,
                    // id: res.data[i].id,
                    id: i + 10,
                    latitude: res.data[i].location.lat,
                    longitude: res.data[i].location.lng,
                    iconPath: "/static/tra/1.png", //图标路径
                    width: 40,
                    height: 40,
                    address: res.data[i].address,
                    label: {
                        content: res.data[i].title,
                        textAlign: 'center'
                    }
                })
            }
            // setData({ //设置markers属性，将搜索结果显示在地图中
            //   markers: mks
            // })
            // markersWX.value.push()
            console.log(markersWX.value);

        },
        fail: function (res) {
            console.log(res);
        },
        complete: function (res) {
            console.log(res);
        }
    });
}

// #endif





























// #ifdef H5

const map = ref(null);
const loaderAMap = ref(null);
const geocoder = ref(null);
const zoom = ref(16)
const latitude = ref(22.833900)
const longitude = ref(108.313000)
const markerContent = ref('<view class="marker PNG"></view>')
const mapMarkerOffsetX = ref(-25)
const mapMarkerOffsetY = ref(-45)

onLoad(async (opts) => {
    console.log(opts);

    name.value = opts.name
    addressText.value = opts.address
    let ll = null
    if (opts.lnglat) {
        ll = opts?.lnglat.split(',')
        longitude.value = ll[0]
        latitude.value = ll[1]
    }

    await initMap()
    getLocation()
})

const initMap = async () => {
    return new Promise((resolve, reject) => {
        (window as any)._AMapSecurityConfig = {
            securityJsCode: "b1b170ac8a0d56e04dad3478ea8f1f9d",
        };
        AMapLoader.load({
            key: "f8aabaa343608b6efdbf4c2e48e6d8b3", // 申请好的Web端开发者Key，首次调用 load 时必填
            version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
            plugins: ["AMap.Scale"], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        })
            .then((AMap) => {
                loaderAMap.value = AMap
                map.value = new AMap.Map("container", {
                    resizeEnable: true,
                    center: [longitude.value, latitude.value],
                    zoom: zoom.value
                });
                // 地理地址-----------------------------------------------
                map.value.plugin(["AMap.Geocoder"], function () { //加载地理编码插件
                    geocoder.value = new AMap.Geocoder({
                        radius: 1000, //以已知坐标为中心点，radius为半径，返回范围内兴趣点和道路信息
                        extensions: "all" //返回地址描述以及附近兴趣点和道路信息，默认“base”
                    });
                    // //返回地理编码结果
                    geocoder.value.on("complete", geocoder_CallBack);
                    // //逆地理编码
                    // that.geocoder.getAddress(new AMap.LngLat(116.359119, 39.972121));
                });
                // 初始化marker-------------------------
                let initMarker = new AMap.Marker({
                    position: new AMap.LngLat(longitude.value, latitude.value),
                    content: markerContent.value,
                    offset: new AMap.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
                    extData: ''
                })
                map.value.add(initMarker)
                // 点击地图---------------------------------
                map.value.on('click', clickMap);
                // --------------------------------------------
                resolve(1)
            })
            .catch((e) => {
                console.log(e);
            });
    })
}

const getLocation = () => {
    let AMap = loaderAMap
    map.value.plugin('AMap.Geolocation', () => {
        var geolocation = new AMap.value.Geolocation({
            enableHighAccuracy: true, // 是否使用高精度定位，默认:true
            timeout: 3000 // 超过3秒后停止定位，默认：无穷大
        });
        geolocation.getCurrentPosition((status, result) => {
            console.log(status, result);
            if (result.status == 1 && result.info === "SUCCESS") {
                longitude.value = result.position.lng;
                latitude.value = result.position.lat;
                // this.$modal.confirm(`${this.latitude}-${this.longitude}`, '自身地理位置')

                // 设置marker与中心点----------

            } else {
                // this.$modal.msg(`获取定位失败:${result.message}`)
                console.error(`Error: ${result.message}`);
            }
        });
    });
}

const clickMap = (e) => {
    console.log(e);
    let AMap = loaderAMap
    let lat = e.lnglat.lat
    let lng = e.lnglat.lng
    console.log([lng, lat]);
    geocoder.value.getAddress(new AMap.value.LngLat(lng, lat))

    longitude.value = lng;
    latitude.value = lat
    // marker
    map.value.clearMap()
    let initMarker = new AMap.value.Marker({
        position: new AMap.value.LngLat(lng, lat),
        content: markerContent.value,
        offset: new AMap.value.Pixel(mapMarkerOffsetX.value, mapMarkerOffsetY.value),
        extData: ''
    })
    map.value.add(initMarker)
}

const geocoder_CallBack = (e) => {
    // console.log(e);
    console.log(e.regeocode.formattedAddress);
    addressText.value = e.regeocode.formattedAddress
}

const selAdd = () => {
    router.back()
    uni.$emit('chooseLocation', {
        name: name,
        address: addressText.value,
        lnglat: [longitude.value, latitude.value]
    })
}

// #endif


























</script>


<style lang="scss" scoped>
::v-deep .marker {
    width: 50px;
    height: 50px;
    display: block;
    // opacity: .5;
}

::v-deep .PNG {
    background: url('../../static/location.png');
    background-size: contain;
}
</style>