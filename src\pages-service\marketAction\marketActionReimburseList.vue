<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '我的报销',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout backRouteName="index" navTitle="我的报销" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white ml3 mt3">
                <wd-calendar v-model="reimBurseDate" use-default-slot @confirm="confirmReimBurseDate"
                    :min-date="minDate" :max-date="maxDate">
                    <wd-button icon="calendar" plain>
                        {{ reimBurseDate ? dayjs(reimBurseDate).format('YYYY-MM-DD') : '选择日期' }}
                    </wd-button>
                </wd-calendar>
                <view class="ml4 font-size-4 font-bold">
                    报销人：{{ userInfo.realname }}
                </view>
            </view>
            <view class="ml3 mr3 mb3 mt2">
                <wd-table :data="reimBurseTable" :height="'70vh'" @row-click="reimBurseTableClick"
                    :fixed-header="false">
                    <wd-table-col prop="reimburseDate" label="报销日期" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="billCode" label="发票号" :width="colWidth">
                        <template #value="{ row }">
                            <view class="color-blue truncate w88px" @click.stop="openPdf(row.billUrl)">
                                {{ row.billCode }}
                            </view>
                        </template>
                    </wd-table-col>
                    <wd-table-col prop="billAmount" label="金额" :width="70"></wd-table-col>
                    <wd-table-col prop="costType_dictText" label="费用类别" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="billType_dictText" label="票据类别" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="70">
                        <template #value="{ row }">
                            <wd-button type="error" size="small" custom-class="" @click.stop="toDel(row)">删除</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="reimBurseTablePageNo"
                    :pageSize="reimBurseTablePageSize" :total="reimBurseTableTotal"
                    @change="reimBurseTablePageChange"></wd-pagination>
                <wd-status-tip v-if="reimBurseTable.length == 0" image="search" tip="当前搜索无结果" />
            </view>
            <wd-popup v-model="showPopup" custom-style="border-top-left-radius:32rpx;border-top-right-radius:32rpx;"
                @close="showPopup = false" position="bottom" :z-index="8888">
                <view class="p2 flex flex-col items-end">
                    <wd-icon class="mb2" name="close" size="16px" @click="showPopup = false"></wd-icon>
                    <iframe :key="iframeKey" :src="showUrl" class="w100% h60vh border-none"></iframe>
                </view>
            </wd-popup>
        </view>
    </PageLayout>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { reimburseList, delActionReimburseById } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const colWidth = ref(100)

const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

const firstChooseDate = ref(false)

// 日期
// const reimBurseDate = ref<number>(Date.now())
const reimBurseDate = ref<number>()
const confirmReimBurseDate = (e) => {
    console.log(e);
    firstChooseDate.value = true
    getReimBurseTable()
}

// iframe弹窗
const showPopup = ref(false)
const showUrl = ref('')
const iframeKey = ref(0)

// 表格
const reimBurseTable = ref([])
const reimBurseTableClick = (e) => {
}

const openPdf = (url)=>{
// #ifdef MP-WEIXIN
uni.downloadFile({
        url: url,
        header: {
            'Access-Control-Allow-Origin': '*'
        },
        success: function (res) {
            let filePath = res.tempFilePath;
            uni.openDocument({
                filePath: filePath,
                // fileType: 'pdf',
                showMenu: true,
                success: function (res) {
                    console.log('打开文档成功');
                },
                complete: function (res) {
                    // toast.info(res)
                }
            });
        },
        fail: function (res) {
            toast.error(`${url}打开失败`)
        }
    });
    // #endif

    // #ifdef H5
    window.location.assign(url)
    // #endif
}

// 分页
const reimBurseTablePageNo = ref<number>(1)
const reimBurseTablePageSize = ref<number>(10)
const reimBurseTableTotal = ref<number>(reimBurseTable.value.length)
const reimBurseTablePageChange = (e) => {
    reimBurseTablePageNo.value = e.value
    getReimBurseTable()
}

const getReimBurseTable = () => {
    let param = {
        pageNo: reimBurseTablePageNo.value,
        pageSize: reimBurseTablePageSize.value,
        reimburseDate: dayjs(reimBurseDate.value).format('YYYY-MM-DD')
    }
    if (!firstChooseDate.value) {
        delete param.reimburseDate
    }
    reimburseList(param).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            reimBurseTable.value = res.result.records
        }
    })
}

const toDel = (e) => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delActionReimburseById({
                id: e.id
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    toast.success('删除成功!')
                    getReimBurseTable()
                }
            })
        })
}

onLoad(() => {
    getReimBurseTable(true)
    uni.$on('refreshList', (e) => {
        toast.success(e)
        getReimBurseTable()
    })
})
onUnload(() => {
    uni.$off('refreshList')
})
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>