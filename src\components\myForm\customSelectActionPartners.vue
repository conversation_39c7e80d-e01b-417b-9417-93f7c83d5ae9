<template>
    <wd-cell :title="label" :title-width="'80px'" :prop="prop">
        <view>
            <view>
                <view v-for="(item, index) in defaultData" :key="index">
                    {{ item.label }}<wd-icon name="delete" size="1rem" color="red" @click="delPeople(item)"></wd-icon>
                </view>
            </view>
            <wd-button type="warning" custom-class="w100%" @click="toSelPartner" :disabled="disabled">选择同行人</wd-button>
        </view>
    </wd-cell>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Object,
        default: () => { }
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const toSelPartner = () => {
    console.log(props.defaultData);

    router.push({
        name: 'marketActionChoosePartners',
        params: {
            defaultData: JSON.stringify(props.defaultData),
            name: props.name
        }
    })
}

const delPeople = (item) => {
    if (props.disabled) {
        return
    }
    uni.$emit('deletePartners', item)
}

</script>

<style lang="scss" scoped></style>