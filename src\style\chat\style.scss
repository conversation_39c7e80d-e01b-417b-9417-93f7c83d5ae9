page {
  background-color: #e5e5e5;
}
@font-face {
  font-family: 'HMfont-home';
  src: url('data:font/woff;charset=utf-8;base64,d09GRgABAAAAAAx8AA0AAAAAEpQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAMYAAAABoAAAAch1cgy0dERUYAAAxAAAAAHgAAAB4AKQAYT1MvMgAAAaAAAABCAAAAVjx1SvhjbWFwAAACDAAAAHYAAAGaa69r/Wdhc3AAAAw4AAAACAAAAAj//wADZ2x5ZgAAAqwAAAeWAAALYGWlZGRoZWFkAAABMAAAADAAAAA2FNI9yWhoZWEAAAFgAAAAHQAAACQH3AOFaG10eAAAAeQAAAAnAAAALA+lAm1sb2NhAAAChAAAACYAAAAmGRYVkG1heHAAAAGAAAAAHwAAACABKQCvbmFtZQAACkQAAAFOAAACcCvYuhBwb3N0AAALlAAAAKMAAADgt9qu0njaY2BkYGAAYsNuzYR4fpuvDNwsDCBw42bNQzjN9L+X+R9zLZDLwcAEEgUAPQUMK3jaY2BkYGBu+N/AEMPCAALM/xgYGVABCwBZ0ANqAAAAeNpjYGRgYBBiWMzAywACTEDMBYQMDP/BfAYAG0YB2QB42mNgZGFgnMDAysDA1Ml0hoGBoR9CM75mMGLkAIoysDIzYAUBaa4pDA7P2F+yMTf8b2CIYW5gaAAKM4LkANv+C+sAAHjaY2GAABYIXsiwnYGHwYmBG0jHMvxiYGLgAMMZDK8ZtgEAWUAGHwB42mNgYGBmgGAZBkYGEJgC5DGC+SwMFUBaikEAKML1jP2Z2jPLZ3bPQp+lPyt9Vv58+vNNL1hfsv3/z8CAIlcCl2P5/1/yj+Q9yTOSJyQ3SS6UnCo5RaJUIlqCU5wbahcOwMjGAFfAyAQkmNAVMAx7AACZsCpmAAAAAAAAAAAAAAA2AK4BSgGaAfACMAKMArADnAQCBJgE9gU2BXAFsAAAeNqNVltsHFcZPv+Z7Iznupe52V57HV92J6ux1+5e49qx0zTJ2k4U0wRFSjZxUGI7MTipIii1oqQ4dinw4BZRpKahiWhTmkqVeIK2DyiE2OKxAkXrB4QotMUCVaFC8AC+zPKfHac2qFaw1v/lnP9c/tt3hgB5nTRzp+iHJEqICPlsokUQIdHCG7YIvGGlCyJY6XzWoS64ol4remWvLNVGRHC9sqjbEuC4ZOsiTCPHca/8RXa1IiGEI+8RnivSNSKTveQQIfHmFDjhQCYe1sDJ4CkJpzVXyBfyDt6CD4KTAt5qArNVA8OyC8ipZWfMGKTzuyDTB9mEC1xrvA8KzTGw6Ufe4243QGcaOqH7H4OqoqqDsXrNUgVDVKKxnpraEM/vPSgqinhwWFRAkYYhaGuHREXVBiDt/Ra6Xfg1bsEzYXlszOPdbk8JGxCJRVU9WCPIddHeGkE3+KIBZlgCiIMaDoGrKkGxHYK6CkfG2BW6XXSXqESp/ID7J3cJPVeISepII2kmcZIkOfIE2UcGyTFCoNXMCMwVDLlVSFsG35LI5m0c7QTBFuyCI2RyrQ5yjieCRew8KSQIlyAOCqjyhENbtLJ1wREwSpAvPOYU7HyBXnNdsy2dHkyn45G6unhd3a/a29ua/L+rrvsMMrvsLQUCUF8uQ30g4C2V73ke6vTePaCoe/fa2/NoNj/72d9U7T6ULOOZ088FNfqu+ztgGw+mZ9jG8TrLvYp27+P/6XYbKdxhu23afXVg87Z4DD1RXeItzHz2wIzc996UlW+efi6sszrZQwLcHS6MEYuTFIsRVoKDVRAEJEIYk92MSQ+nAHAcJRzQQE9BNs8CyB1arZXc2h/WYmE+2dUP0N9FX6tyb2Z4gtKJ4Sr9ecg0t5smfb3XagRotHqhv9Pb08nsOuFOZ//avx9aIoVTYKDxdqNax3LlJe5f3GVSS9pIF7tfDgvWbG3hTVaf2DQ5rOYcXm+LlNHKEnTAkqCA/Wd0YUlSubC3suh9gtFqWCxDAwbpk/KCt4r6toV52Ib6KtxYsu0lVbaAcUWWVfgQzRbL/rLyIjSs7kH7+QXg0H5tYd5bZT1XmeeGuF1YjVG8LQmweGGj+TGLo5b7XHMeRpBeLh4+CUcPFI+NAZz3/jSY6U27xe4nAIYqsWSyN5nctX//wjcu/GJf8cgfZ7/3l8nBhtN9O0uxgfarBw9/93nYsWsH/lgPbCMnicDd4igxSIKg/4TozWnLxNw5mMoUCCyh2byeqB6OzW/ZMTB4gYdmDXsM+x5TnQvjHX9Gr2lG9IiTm+hJXRqI7YsaP03FtFBIi6WSbYy3wUdrexodp5HeqdLfGJr3d6uDyi9LwuPbava3wW3N8B5ALJhMJrUYQExDIRiDveA0rr0USwAkYvRiowOYUrJcucsJ3G4SYrjImhKREDszbYNl0BVvtxKzZXjLOy7bMQXuKjDqnZUtFKdhWolZsndW2Y7+y5jrP3Ae10JqEAFaSCfpxb4/SmbI98mr5A08CP03M1grgRa+EWEunS+ggsnYUBysqzBTWBgCzN5HwGyisHnGbuE3ZgLZFCIzK0gWYB4zjAiDAcU8szr1w8rwM9cHAayInJ8PnOf8fjIRVcxqhhxKnCy8D1mwZU3DMdBuPBRGULA0bfVbEtNMTRv0BwYe6jRPVYnKKuWk8aGvzHJskNLZsRckTZOgj9EXxma9ytAowOhQlWagmMsVYXnZ5/QDyDo3bzrZv2rybdydkRBo8reZjOTLmuzdr+7WIWtHtpp4cmPxpKQrQdmwBkdrhKlTmhxSDPHci8q475GpjSsvDtAzB5YPnKE+g7dzRUqLubvpGl/AvPKkpjKHGHAF8xomEdJEXFbb8TQ+S7nmahR9SI+3GpgT1FKA8d8Kw2GZwpWnvQ640GNEo8loFGa6VKveUqvkO4vex9jijYuL0Iid/fHigreCemBhAQKor4zLVyV1GgrAViajner6OtWC68wcgYXnEVhw+epuhijr6xYQYf7LF5nYxEE/iuQEVuZWd231C60H1stzXbczfh0xvqtaW/54vJVV9rpRZh24KXmET96Pj2kRgIj2RWz0mKbr6zJ0oPITfxZalRBy7sr/et2/aWf0egXe09XjWiSiHVf1X+obm+s/8ll16Lqu3aya0YgCtZrO8CzweaxERLR6xLQ0RkrIWCHBzoccoesxlA2Us0FA4uhbvQBfSsHISMfFi6UU7NzZceCtUimRGH9ETOgrqTNe5UzqWcg+W+rovd6TGv5AKZV2nHXgxqM8JgzTrlV+z53j2jDHxH9JHVajTfiF11/99AtCGNOTwVyuowom6gR/+TXua1JUF2+JUemWpDeIX6VTp/jSBRpp0OnLXBt9dcrre1oyotLbknhbrjelSbhb+jqlF0+uPQjqepDql17B8yn5tFLmTM7FyLVgr2DpsKPYiVUQNJtzGZ2d7cMOXgU+nZiTXGlmZGQG2dzEO970O+bhSUonD1fpUWnuXGmK0qnSuTnpqOtyLj3/VIX4FkCeOo/v37v4/g1uvH8MWgWEP6H65RBAzdzQ2MvDPr/g+c3vHzRtev/I+gPI/V8v4H8AJxdZLwAAeNp1kL1OwzAUhU/6h2gRAwhmTwwFpT9jRyqVLpUQQ5nb1Amtkjhy3EodYOIJ2HgMHoAnYOMBeBZOwoWhorZ89fn43uNrAzjBFzz8jDNcCXto4Fa4ggNEwlXqj8I18otwHS28CTc4P4SbuPTawi2cek908GqH3LVLt4I9HOFauIJj3AtXqRvhGvlZuI5zvAo3qL8LNzHFp3ALF96UyhAWGjM4xgUU5tgyLhHQOUVYRsc8NIdWz5xeqPlWLQOThiZ1lMeY/GVhPClU4I5mEdaIaWy51dE6ntk9yf+KUzpY5GykOFDowUeXsrb50qSq53f3FN6wMC2Ld9+UY8Om+lQdyxSXZXFCGomJZsMxWSErz1ZUAuo+bXWq7e/z803Udy5UoTWJGvFaHcdGZdasdOCY/FDekWGADme44+6Xf5swzbls0OmEYuAHJsE3XKtneQAAeNpdjF0OgjAQhFlU/qpovAcJNdF4ndJUu0S3FVqTenqB+uQ87GRnJl+SJlHVciH513FJU0hhBWvYQAY5FFBCBQy2sIMa9nCoURq6GXJ3L6jDan4bp0bHc+ctCpK1Rbr3KKzAjxaGzYtRuZZfL8w4rYbmbVCq3/zM2pb3SIMPxmcPH5BOZYfCvCaKLCYO2QmahbmRLJpW2mOMePEMEfgFeMM46QAAAAAB//8AAgABAAAADAAAABYAAAACAAEAAwARAAEABAAAAAIAAAAAeNpjYGBgZACCq0vUOUD0jZs1D2E0AEhDB8YAAA==')
    format('woff');
  //src:url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2');}
}
.icon {
  font-family: 'HMfont-home' !important;
  font-size: 32upx;
  font-style: normal;
  &.biaoqing:before {
    content: '\e797';
  }
  &.jianpan:before {
    content: '\e7b2';
  }
  &.yuyin:before {
    content: '\e805';
  }
  &.tupian:before {
    content: '\e639';
  }
  &.chehui:before {
    content: '\e904';
  }
  &.luyin:before {
    content: '\e905';
  }
  &.luyin2:before {
    content: '\e677';
  }
  &.other-voice:before {
    content: '\e667';
  }
  &.my-voice:before {
    content: '\e906';
  }
  &.hongbao:before {
    content: '\e626';
  }
  &.tupian2:before {
    content: '\e674';
  }
  &.paizhao:before {
    content: '\e63e';
  }
  &.add:before {
    content: '\e655';
  }
  &.close:before {
    content: '\e607';
  }
  &.to:before {
    content: '\e675';
  }
}
.hidden {
  display: none !important;
}
.popup-layer {
  transition: all 0.15s linear;
  width: 98%;
  height: 42vw;
  padding: 20upx 2%;
  background-color: #f3f3f3;
  border-top: solid 1upx #ddd;
  position: fixed;
  z-index: 20;
  top: 100%;
  &.showLayer {
    transform: translate3d(0, -42vw, 0);
  }
  .emoji-swiper {
    height: 40vw;
    swiper-item {
      display: flex;
      align-content: flex-start;
      flex-wrap: wrap;
      view {
        width: 12vw;
        height: 12vw;
        display: flex;
        justify-content: center;
        align-items: center;
        image {
          width: 8.4vw;
          height: 8.4vw;
        }
      }
    }
  }
  .more-layer {
    width: 100%;
    height: 42vw;
    .list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .box {
        width: 18vw;
        height: 18vw;
        border-radius: 20upx;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 3vw 2vw 3vw;
        .icon {
          font-size: 70upx;
        }
      }
    }
  }
}
.input-box {
  width: 98%;
  min-height: 100upx;
  padding: 0 1%;
  background-color: #f2f2f2;
  display: flex;
  position: fixed;
  z-index: 20;
  bottom: -2upx;
  transition: all 0.15s linear;
  border-bottom: solid 1upx #ddd;
  &.showLayer {
    transform: translate3d(0, -42vw, 0);
  }
  .voice,
  .more {
    flex-shrink: 0;
    width: 90upx;
    height: 100upx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .send {
    //H5发送按钮左边距
    /* #ifdef H5 */
    margin-left: 20upx;
    /* #endif */
    flex-shrink: 0;
    width: 100upx;
    height: 100upx;
    display: flex;
    align-items: center;
    .btn {
      width: 90upx;
      height: 56upx;
      display: flex;
      justify-content: center;
      align-items: center;
      background: linear-gradient(to right, #00aaff, #55aaff);
      color: #fff;
      border-radius: 6upx;
      font-size: 24upx;
    }
  }
  .textbox {
    width: 100%;
    min-height: 70upx;
    margin-top: 15upx;
    .voice-mode {
      width: calc(100% - 2upx);
      height: 68upx;
      border-radius: 70upx;
      border: solid 1upx #cdcdcd;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28upx;
      background-color: #fff;
      color: #555;
      &.recording {
        background-color: #e5e5e5;
      }
    }
    .text-mode {
      width: 100%;
      min-height: 70upx;
      display: flex;
      background-color: #fff;
      border-radius: 40upx;
      .box {
        width: 100%;
        padding-left: 30upx;
        min-height: 70upx;
        display: flex;
        align-items: center;
        textarea {
          width: 100%;
        }
      }
      .em {
        flex-shrink: 0;
        width: 80upx;
        padding-left: 10upx;
        height: 70upx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
.record {
  width: 40vw;
  height: 40vw;
  position: fixed;
  top: 55%;
  left: 30%;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 20upx;
  .ing {
    width: 100%;
    height: 30vw;
    display: flex;
    justify-content: center;
    align-items: center;
    // 模拟录音音效动画
    @keyframes volatility {
      0% {
        background-position: 0% 130%;
      }
      20% {
        background-position: 0% 150%;
      }
      30% {
        background-position: 0% 155%;
      }
      40% {
        background-position: 0% 150%;
      }
      50% {
        background-position: 0% 145%;
      }
      70% {
        background-position: 0% 150%;
      }
      80% {
        background-position: 0% 155%;
      }
      90% {
        background-position: 0% 140%;
      }
      100% {
        background-position: 0% 135%;
      }
    }
    .icon {
      background-image: linear-gradient(to bottom, #f09b37, #fff 50%);
      background-size: 100% 200%;
      animation: volatility 1.5s ease-in-out -1.5s infinite alternate;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 150upx;
      color: #f09b37;
    }
  }
  .cancel {
    width: 100%;
    height: 30vw;
    display: flex;
    justify-content: center;
    align-items: center;
    .icon {
      color: #fff;
      font-size: 150upx;
    }
  }
  .tis {
    width: 100%;
    height: 10vw;
    display: flex;
    justify-content: center;
    font-size: 28upx;
    color: #fff;
    &.change {
      color: #f09b37;
    }
  }
}
.content {
  width: 100%;
  .msg-list {
    width: 96%;
    padding: 0 2%;
    position: absolute;
    top: 0;
    bottom: 100upx;
    .loading {
      //loading动画
      display: flex;
      justify-content: center;
      @keyframes stretchdelay {
        0%,
        40%,
        100% {
          transform: scaleY(0.6);
        }
        20% {
          transform: scaleY(1);
        }
      }
      .spinner {
        margin: 20upx 0;
        width: 60upx;
        height: 100upx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        view {
          background-color: #55aaff;
          height: 50upx;
          width: 6upx;
          border-radius: 6upx;
          animation: stretchdelay 1.2s infinite ease-in-out;
        }
        .rect2 {
          animation-delay: -1.1s;
        }
        .rect3 {
          animation-delay: -1s;
        }
        .rect4 {
          animation-delay: -0.9s;
        }
        .rect5 {
          animation-delay: -0.8s;
        }
      }
    }
    .row {
      padding: 20upx 0;
      .system {
        display: flex;
        justify-content: center;
        view {
          padding: 0 30upx;
          height: 50upx;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #c9c9c9;
          color: #fff;
          font-size: 24upx;
          border-radius: 40upx;
        }
        .red-envelope {
          image {
            margin-right: 5upx;
            width: 30upx;
            height: 30upx;
          }
        }
      }
      &:first-child {
        margin-top: 20upx;
      }
      .my .left,
      .other .right {
        width: 100%;
        display: flex;
        .bubble {
          max-width: 70%;
          min-height: 50upx;
          border-radius: 10upx;
          padding: 15upx 20upx;
          display: flex;
          align-items: center;
          font-size: 32upx;
          word-break: break-word;
          &.img {
            background-color: transparent;
            padding: 0;
            overflow: hidden;
            image {
              max-width: 350upx;
              max-height: 350upx;
            }
          }
          &.red-envelope {
            background-color: transparent;
            padding: 0;
            overflow: hidden;
            position: relative;
            justify-content: center;
            align-items: flex-start;
            image {
              width: 250upx;
              height: 313upx;
            }
            .tis {
              position: absolute;
              top: 6%;
              font-size: 26upx;
              color: #9c1712;
            }
            .blessing {
              position: absolute;
              bottom: 14%;
              color: #e9b874;
              width: 80%;
              text-align: center;
              overflow: hidden;
              // 最多两行
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
            }
          }
          &.voice {
            .icon {
              font-size: 40upx;
              display: flex;
              align-items: center;
            }
            .icon:after {
              content: ' ';
              width: 53upx;
              height: 53upx;
              border-radius: 100%;
              position: absolute;
              box-sizing: border-box;
            }
            .length {
              font-size: 28upx;
            }
          }
        }
      }
      .my .right,
      .other .left {
        flex-shrink: 0;
        width: 80upx;
        height: 80upx;
        image {
          width: 80upx;
          height: 80upx;
          border-radius: 10upx;
        }
      }
      .my {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        .left {
          min-height: 80upx;

          align-items: center;
          justify-content: flex-end;
          .bubble {
            background-color: #55aaff;
            color: #fff;

            &.voice {
              .icon {
                color: #fff;
              }
              .length {
                margin-right: 20upx;
              }
            }
            &.play {
              @keyframes my-play {
                0% {
                  transform: translateX(80%);
                }
                100% {
                  transform: translateX(0%);
                }
              }
              .icon:after {
                border-left: solid 10upx rgba(240, 108, 122, 0.5);
                animation: my-play 1s linear infinite;
              }
            }
          }
        }
        .right {
          margin-left: 15upx;
        }
      }
      .other {
        width: 100%;
        display: flex;
        .left {
          margin-right: 15upx;
        }
        .right {
          flex-wrap: wrap;
          .username {
            width: 100%;
            height: 45upx;
            font-size: 24upx;
            color: #999;
            display: flex;
            .name {
              margin-right: 50upx;
            }
          }
          .bubble {
            background-color: #fff;
            color: #333;
            &.voice {
              .icon {
                color: #333;
              }
              .length {
                margin-left: 20upx;
              }
            }
            &.play {
              @keyframes other-play {
                0% {
                  transform: translateX(-80%);
                }
                100% {
                  transform: translateX(0%);
                }
              }
              .icon:after {
                border-right: solid 10upx rgba(255, 255, 255, 0.8);

                animation: other-play 1s linear infinite;
              }
            }
          }
        }
      }
    }
  }
}
.windows {
  .mask {
    position: fixed;
    top: 100%;
    width: 100%;
    height: 100%;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 0;
    transition: opacity 0.2s ease-out;
  }
  .layer {
    position: fixed;
    width: 80%;
    height: 70%;
    left: 10%;
    z-index: 1001;
    border-radius: 20upx;
    overflow: hidden;
    top: 100%;
    transform: scale3d(0.5, 0.5, 1);
    transition: all 0.2s ease-out;
  }
  &.show {
    display: block;
    .mask {
      top: 0;
      opacity: 1;
    }
    .layer {
      transform: translate3d(0, -85vh, 0) scale3d(1, 1, 1);
    }
  }
  &.hide {
    display: block;
    .mask {
      top: 0;
      opacity: 0;
    }
    .layer {
      //transform: translate3d(0,-85vh,0) scale3d(.5,.5,1);
    }
  }
}
.open-redenvelope {
  width: 100%;
  height: 70vh;
  background-color: #cf3c35;
  position: relative;
  .top {
    width: 100%;
    background-color: #fe5454;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    border-radius: 0 0 100% 100%;
    box-shadow: inset 0 -20upx 0 #9c1712;
    .close-btn {
      width: 100%;
      height: 80upx;
      display: flex;
      justify-content: flex-end;
      margin-bottom: 30upx;
      .icon {
        color: #9c1712;
        margin-top: 10upx;
        margin-right: 10upx;
      }
    }
    image {
      width: 130upx;
      height: 130upx;
      border: solid 12upx #cf3c35;
      border-radius: 100%;

      margin-bottom: -65upx;
    }
    margin-bottom: 65upx;
  }
  .from,
  .blessing,
  .money,
  .showDetails {
    width: 90%;
    padding: 5upx 5%;
    display: flex;
    justify-content: center;
    font-size: 32upx;
    color: #fff;
  }
  .money {
    font-size: 100upx;
    color: #f8d757;
    display: flex;
    padding-top: 20upx;
  }
  .showDetails {
    position: absolute;
    bottom: 20upx;
    align-items: center;
    font-size: 28upx;
    color: #f8d757;
    .icon {
      font-size: 26upx;
      color: #f8d757;
    }
  }
}
// ============= 自定义========
.chat-item {
  .icon:after {
    content: ' ';
    width: 27px;
    height: 27px;
    border-radius: 100%;
    position: absolute;
    box-sizing: border-box;
  }
  .play {
    position: relative;
    @keyframes my-play {
      0% {
        transform: translateX(80%);
      }
      100% {
        transform: translateX(0%);
      }
    }
    .icon:after {
      top: 0;
      right: 0;
      border-left: solid 10upx rgba(240, 108, 122, 0.5);
      animation: my-play 1s linear infinite;
    }
  }
}
