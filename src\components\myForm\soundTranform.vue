<template>
    <!-- #ifdef H5 -->
    <view class="flex flex-col justify-start items-end pr4 mb1">
        <wd-button v-if="!isRecording" custom-class="" :disabled="disabled" @click="startRecord" type="error"
            size="small">录音识别</wd-button>
        <wd-button v-else custom-class="" @click="stopRecord" type="error" size="small">
            结束录音({{ duration }})秒
        </wd-button>
        <view class="flex flex-wrap justify-end">
            <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudioH5(item)">
                <view class="w24 pt2 pb2 pl1 pr5 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                    <view class="">{{ item.duration }}秒</view>
                    <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                        src="/static/index/audioplay.png" />
                    <wd-icon class="absolute right-0 top-0" name="close-bold" size="22px"
                        @click.stop="delSound(item)"></wd-icon>
                </view>
            </view>
        </view>
    </view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <view class="flex flex-col justify-start items-end pr4 mb1">
        <wd-button v-if="!isRecording" custom-class="" :disabled="disabled" @click="startRecordWX" type="error"
            size="small">录音识别</wd-button>
        <wd-button v-else custom-class="" @click="stopRecordWX" type="error" size="small">
            结束录音({{ duration }})秒
        </wd-button>
        <view class="flex flex-wrap justify-end">
            <view v-for="(item, index) in audioPlayUrls" :key="index" @click="playAudioWX(item)">
                <view class="w24 pt2 pb2 pl1 pr0 ml1 mr1 mt1 bg-#ff6350 color-#ffffff rd-2 center relative">
                    <view class="">{{ item.duration }}秒</view>
                    <wd-img v-if="item.playing" :width="20" :height="20" mode="aspectFill"
                        src="/static/index/audioplay.png" />
                    <wd-icon custom-class="ml3" class="absolute right-0 top-0" name="close-bold" size="22px"
                        @click.stop="delSound(item)"></wd-icon>
                </view>
            </view>
        </view>
    </view>
    <!-- #endif -->
    <wd-popup v-model="showOverLay" custom-style="border-radius:16rpx;" @close="showOverLay = false" position="center"
        :close-on-click-modal="true">
        <view class="h35vh bg-white flex flex-col items-center">
            <view class="w60vw">
                <wd-textarea v-model="tempAudioResult" placeholder="" />
            </view>
            <wd-button custom-class="w80%" @click="addText" type="error">添加到文本</wd-button>
        </view>
    </wd-popup>
    <wd-overlay :show="isTransforming">
        <view class="flex items-center justify-center h100%">
            <view class="w30 h15 bg-#ffffff center font-size-6 rd-2">转写中...</view>
        </view>
    </wd-overlay>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'
import { useUserStore } from '@/store'
import { getAudioTransform } from '@/service/index/foo'
const toast = useToast()
const userStore = useUserStore()
const userInfo = useUserStore().userInfo
const router = useRouter()
const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Array,
        default: () => []
    },
    labelWidth: {
        type: String,
        default: ''
    },
    placeholder: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits<{
    (event: string, ...args: any[]): void
}>()


const showMask = ref(false)


// 音频播放路径
const audioPlayUrls = ref([
    // {
    //     url: 'http://qn.gxcf.ltd/audio/trans/20250507/recording_2025-05-07T01-23-13-571Z_1746580995059.wav',
    //     duration: 3,
    //     playing: false
    // },
])
// 展示结果
const showOverLay = ref(false)
// 转写结果
const tempAudioResult = ref('')
// 记录中
const isRecording = ref(false)
// 时长
const duration = ref(0)
// 计时器
const timer = ref(null)
// 任务ID
const taskId = ref('')
// 是否转写中
const isTransforming = ref(false)
// 请求url
const apiUrl = import.meta.env.VITE_SERVER_BASEURL
const actionUrl: string = `${apiUrl}/jeecg-action/action/visitRecords/audio/upload/trans`

// 监听语音链接
watch(
    () => props.defaultData,
    (n, o) => {
        console.log(n, o);
        audioPlayUrls.value = n
    }
)



// 轮询获取语音
const getAudioResult = async () => {

    if (!taskId.value) {
        isTransforming.value = false;
        return;
    }

    try {
        const response: any = await getAudioTransform({ taskId: taskId.value });

        console.log(response);

        if (response.success && response.result) {
            // 解析返回的结果JSON
            const resultObj = response.result;//JSON.parse(response.result);
            if (resultObj && resultObj.Result && resultObj.Result.Sentences) {
                // 提取所有句子的文本并连接
                const sentences = resultObj.Result.Sentences.map(s => s.Text).join(' ');
                tempAudioResult.value = sentences;
                // 展示文本框
                showOverLay.value = true;
                // 
                isTransforming.value = false;

                console.log(audioPlayUrls.value);

                audioPlayUrls.value.find(item => item.taskId == taskId.value).content = sentences;

                uni.$emit('setAudioUrls', {
                    param: audioPlayUrls.value,
                    name: props.name
                })
            } else {
                // 如果还在转写中，继续轮询
                setTimeout(getAudioResult, 2000);
            }
        } else if (response.success) {
            // 如果返回成功但结果为空，继续轮询
            setTimeout(getAudioResult, 2000);
        } else {
            uni.$emit('setAudioUrls', {
                param: audioPlayUrls.value,
                name: props.name
            })
            console.log(response.message || '获取转写结果失败');
            isTransforming.value = false;
        }
    } catch (error) {
        uni.$emit('setAudioUrls', {
            param: audioPlayUrls.value,
            name: props.name
        })
        console.error('获取转写结果失败:', error);
        console.log('获取转写结果失败: ' + (error.message || '未知错误'));
        isTransforming.value = false;
    }
}

// 添加文本到某处
const addText = () => {
    showOverLay.value = false
    emit('addText', {
        data: tempAudioResult.value,
    })
}

// 删除音频
const delSound = (ele) => {
    console.log(props);

    audioPlayUrls.value = audioPlayUrls.value.filter(item => item.url != ele.url)
    uni.$emit('setAudioUrls', {
        param: audioPlayUrls.value,
        name: props.name
    })
}

// #ifdef H5

// 录音API
const mediaRecorder = ref(null)
// 录音路径URL
const audioUrl = ref('')
// 构造二进制数据
const audioChunks = ref([])


// 播放音频
let innerAudioContext = null
const playAudio = (element) => {
    if (innerAudioContext) {
        innerAudioContext.pause()
        innerAudioContext.destroy()
        innerAudioContext = null
        innerAudioContext = uni.createInnerAudioContext()
        console.log('销毁');

    } else {
        innerAudioContext = uni.createInnerAudioContext()
    }

    innerAudioContext.onPlay(() => {
        console.log('开始播放');
    });
    innerAudioContext.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
    });
    innerAudioContext.onEnded(() => {
        console.log('自然播放结束');
        element.playing = false
    })

    innerAudioContext.src = element.url;
    innerAudioContext.play()
    audioPlayUrls.value.forEach(e => {
        e.playing = false
    })
    element.playing = true
}

// H5播放音频
const audioPlayer = ref(null)
const playAudioH5 = (element) => {
    // 如果已有实例，先销毁
    if (audioPlayer.value) {
        audioPlayer.value.pause()
        audioPlayer.value.src = ''
        audioPlayer.value = null
    }

    // 创建新的 Audio 实例
    audioPlayer.value = new Audio(element.url)

    audioPlayer.value.addEventListener('play', () => {
    })

    audioPlayer.value.addEventListener('ended', () => {
        element.playing = false
        console.log('播放结束');
    })

    audioPlayer.value.addEventListener('error', (e) => {
        console.error('音频播放错误:', e)
    })

    audioPlayer.value.play()
    audioPlayUrls.value.forEach(e => {
        e.playing = false
    })
    element.playing = true

}

// 开始录音
const startRecord = async () => {

    // 
    // showOverLay.value = true;
    // return

    if (isRecording.value) return;

    try {
        // mediaDevices只适用于https或者localhost
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            uni.showToast({
                title: 'getUserMedia is not supported in this browser',
                icon: 'none'
            });
            return
        }

        isRecording.value = true;
        isTransforming.value = true;
        audioChunks.value = [];
        duration.value = 0;

        // 开始计时
        timer.value = setInterval(() => {
            duration.value++;
        }, 1000);

        // 获取麦克风权限
        console.log(navigator.mediaDevices);
        // const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        mediaRecorder.value = new MediaRecorder(stream);

        // 创建媒体记录器
        // const MediaRecorder = window.MediaRecorder || window.webkitMediaRecorder;
        mediaRecorder.value = new MediaRecorder(stream);

        mediaRecorder.value.ondataavailable = (e) => {
            if (e.data.size > 0) {
                audioChunks.value.push(e.data);
            }
        };

        mediaRecorder.value.onstop = () => {
            const audioBlob = new Blob(audioChunks.value, { type: 'audio/wav' });
            audioUrl.value = URL.createObjectURL(audioBlob);
            console.log(audioUrl.value, duration.value, audioBlob);
            // 文件类型对象
            let file = new File([audioBlob], `${userInfo.realname}${dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss')}.wav`, { type: 'audio/wav' })
            // 使用formData格式传参
            let formData = new FormData()
            formData.append('file', file)

            console.log(file);

            uni.uploadFile({
                url: actionUrl,
                file: file,
                name: 'file',
                header: {
                    'X-Access-Token': userStore.userInfo.token,
                    'X-Tenant-Id': userStore.userInfo.tenantId,
                },
                success: (res: any) => {
                    console.log(res);
                    if (res.statusCode == 200) {
                        let RES = JSON.parse(res.data)

                        taskId.value = RES.result.taskId
                        audioPlayUrls.value.push({
                            taskId: RES.result.taskId,
                            url: RES.result.fileUrl,
                            duration: duration.value,
                            content: '',
                            playing: false
                        })
                        console.log(taskId.value);
                        // 请求结果 
                        getAudioResult()
                    }
                },
                fail: (err) => {
                    isTransforming.value = false;
                    console.error(err)
                },
                complete: () => {
                }
            })

            // 停止所有音轨
            stream.getTracks().forEach(track => track.stop());
        };

        mediaRecorder.value.start(100); // 每100ms收集一次数据
    } catch (error) {
        console.error('录音失败:', error);
        uni.showToast({
            title: `录音失败，请检查麦克风权限:${error}`,
            icon: 'none'
        });
        isTransforming.value = false;
        stopRecord();
    }
}

// 停止录音
const stopRecord = () => {
    if (!isRecording) return;

    isRecording.value = false;
    clearInterval(timer.value);

    if (mediaRecorder.value && mediaRecorder.value.state !== 'inactive') {
        mediaRecorder.value.stop();
    }
}

// #endif




// #ifdef MP-WEIXIN

const recorderManager = uni.getRecorderManager();


recorderManager.onStop((res) => {
    console.log(res);

    isTransforming.value = true;

    uni.uploadFile({
        url: actionUrl,
        filePath: res.tempFilePath,
        name: 'file',
        header: {
            'X-Access-Token': userStore.userInfo.token,
            'X-Tenant-Id': userStore.userInfo.tenantId,
        },
        success: (res: any) => {
            console.log(res);
            if (res.statusCode == 200) {
                let RES = JSON.parse(res.data)

                taskId.value = RES.result.taskId
                audioPlayUrls.value.push({
                    taskId: RES.result.taskId,
                    url: RES.result.fileUrl,
                    duration: duration.value,
                    content: '',
                    playing: false
                })
                console.log(taskId.value);
                // 请求结果 
                getAudioResult()
            }
        },
        fail: (err) => {
            isTransforming.value = false;
            console.error(err)
        }
    })
})

recorderManager.onError((err) => {
    console.log(err);
    duration.value = 0
    clearInterval(timer.value)
    isRecording.value = false
    toast.error(err.errMsg)
})

const startRecordWX = () => {

    wx.authorize({
        scope: 'scope.record',
        success() {
            // 用户同意授权，可以进行录音操作


            isRecording.value = true

            duration.value = 0;
            // 开始计时
            timer.value = setInterval(() => {
                duration.value++;
            }, 1000);

            recorderManager.start({
                format: 'wav'
            })



        },
        fail() {
            // 用户拒绝授权，可以引导用户手动开启权限
            wx.showModal({
                title: '提示',
                content: '您未授权录音权限，是否前往设置？',
                success(res) {
                    if (res.confirm) {
                        wx.openSetting({
                            success(settingData) {
                                if (settingData.authSetting['scope.record']) {
                                    console.log('用户已授权录音权限');
                                } else {
                                    console.log('用户未授权录音权限');
                                }
                            }
                        });
                    }
                }
            });
        }
    });
}

const stopRecordWX = () => {
    console.log(recorderManager);

    isRecording.value = false

    clearInterval(timer.value);

    recorderManager.stop()
}

let innerAudioContextWX = null
const playAudioWX = (element) => {

    if (innerAudioContextWX) {
        innerAudioContextWX.pause()
        innerAudioContextWX.destroy()
        innerAudioContextWX = null
        innerAudioContextWX = uni.createInnerAudioContext()

        innerAudioContextWX.obeyMuteSwitch = false;

        innerAudioContextWX.autoplay = true;
        console.log('销毁');

    } else {
        innerAudioContextWX = uni.createInnerAudioContext()
    }

    innerAudioContextWX.onPlay(() => {
        console.log('开始播放');
    });
    innerAudioContextWX.onError((res) => {
        console.log(res.errMsg);
        console.log(res.errCode);
    });
    innerAudioContextWX.onEnded(() => {
        console.log('自然播放结束');
        element.playing = false
    })

    innerAudioContextWX.src = element.url;
    innerAudioContextWX.play()
    audioPlayUrls.value.forEach(e => {
        e.playing = false
    })
    element.playing = true
}

// #endif







// #ifdef H5
onLoad(() => {
    audioPlayUrls.value = props.defaultData
})
// #endif


// #ifdef MP-WEIXIN
onReady(() => {
    audioPlayUrls.value = props.defaultData
})
// #endif










</script>

<style lang="scss" scoped>
// ::v-deep .textareaborder {
//     border: 1px solid rgb(226, 223, 223);
//     border-radius: 6px;
// }</style>