<template>
    <view class="">
        <wd-cell :title="label" :value="text" :title-width="'80px'" :prop="prop">
            <view v-if="text" class="text-left b-b-style-solid b-b-blue">{{ text }}</view>
            <view v-else class="text-left color-#bfbfbf">复制地址或在地图上选取地址</view>
        </wd-cell>
        <view class="flex align-center justify-end mb2 mt0 mr3">
            <view class="flex flex-items-center" v-if="showCopy" @click="copy()">
                <wd-icon name="file-copy" size="20px"></wd-icon>
                <wd-button size="small" type="info" plain hairline :disabled="disabled">复制以上地址</wd-button>
            </view>
            <view class="flex flex-items-center ml3" @click="toSelLocation">
                <wd-icon name="location" size="20px"></wd-icon>
                <wd-button size="small" type="info" plain hairline :disabled="disabled">选择位置</wd-button>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: ''
    },
    showCopy: {
        type: Boolean,
        default: false
    },
    defaultData: {
        type: Object,
        default: () => { }
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const copy = () => {
    if (props.disabled) {
        return
    }
    uni.$emit('copyLocation')
}

const toSelLocation = () => {
    if (props.disabled) {
        return
    }
    router.push({
        name: 'marketActionChooseLocation',
        params: {
            ...props.defaultData,
            name: props.name
        }
    })
}

</script>

<style lang="scss" scoped></style>