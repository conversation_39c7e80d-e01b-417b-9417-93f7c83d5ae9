<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout :navTitle="customTitle" :showFab="false">
        <scroll-view scroll-y>
            <customForm :formConfig="formConfig" :formData="formData" :rules="rules" @setFormData="setFormData"
                @customSetFormData="customSetFormData" @submit="submit" btnText1="提交" :disabledBtn="action == 'check'">
            </customForm>
        </scroll-view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './marketActionRestRegisterFormConfig'
import { addRestRegister, editRestRegister, getRestRegisterById } from '@/service/marketAction/marketAction'

const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo


const action = ref('')
const customTitle = computed(() => {
    if (action.value == 'add') {
        return '添加休息备案'
    } else if (action.value == 'edit') {
        return '修改休息备案'
    } else if (action.value == 'check') {
        return '查看休息备案'
    }
})

const now = ref(dayjs(Date.now()).format('YYYY-MM-DD'))
console.log(now.value, new Date(`${now.value}T00:00:00`));
const formData = ref({
    startTime: new Date(`${now.value}T00:00:00`).getTime(),
    endTime: new Date(`${now.value}T00:00:00`).getTime(),
    duration: '1天',
    remark: ''
})

// 原组件表单值改变后可进行其他操作--------------------------
const setFormData = (e) => {
    console.log(e);
    calcDay()
}
// 自定义组件表单值改变后可进行其他操作--------------------------
const customSetFormData = (e) => {
    console.log(e);
}

// 计算休息时长
const calcDayTimeStamp = ref(1000 * 3600 * 24)
const calcDay = () => {
    console.log(new Date(formData.value.endTime), formData.value.endTime);
    console.log(new Date(formData.value.startTime), formData.value.startTime);

    let time = formData.value.endTime - formData.value.startTime
    calcDayTimeStamp.value = time + (1000 * 3600 * 24)
    console.log(time / (1000 * 3600 * 24));
    formData.value.duration = `${time / (1000 * 3600 * 24) + 1}天`
}

onLoad((opts) => {
    console.log(opts);
    action.value = opts.action

    // 编辑
    if (opts.action == 'edit') {
        getRestRegisterById({ id: opts.id }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                calcDayTimeStamp.value = res.result.duration
                res.result.duration = `${res.result.duration / (1000 * 3600 * 24)}天`
                res.result.startTime = new Date(`${res.result.startTime}T00:00:00`).getTime()
                res.result.endTime = new Date(`${res.result.endTime}T00:00:00`).getTime()
                formData.value = res.result
            }
        })
        setFormDisAbled(false)
    }
    // 查看
    if (opts.action == 'check') {
        getRestRegisterById({ id: opts.id }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                calcDayTimeStamp.value = res.result.duration
                res.result.duration = `${res.result.duration / (1000 * 3600 * 24)}天`
                res.result.startTime = new Date(`${res.result.startTime}T00:00:00`).getTime()
                res.result.endTime = new Date(`${res.result.endTime}T00:00:00`).getTime()
                formData.value = res.result
            }
        })
        setFormDisAbled(true)
        console.log(formConfig.value);
    } else {
        setFormDisAbled(false)
    }
})


const setFormDisAbled = (flag) => {
    formConfig.value[0].form = formConfig.value[0].form.map(item => ({
        ...item,
        disabled: flag
    }))
}

const submit = () => {
    // console.log(formData.value);
    // return
    if (formData.value.duration.indexOf('-') != -1 || formData.value.duration == '0天') {
        toast.error('天数不能为0或者负数')
        return
    }

    if (action.value == 'add') {
        let data = JSON.parse(JSON.stringify(formData.value))
        data.startTime = dayjs(data.startTime).format('YYYY-MM-DD')
        data.endTime = dayjs(data.endTime).format('YYYY-MM-DD')
        data.userId = userInfo.id
        data.userName = userInfo.realname
        data.duration = calcDayTimeStamp.value
        console.log(data);

        addRestRegister(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '添加成功！')
            } else {
                toast.error(res.message)
            }
        })
    }

    if (action.value == 'edit') {
        let data = JSON.parse(JSON.stringify(formData.value))
        data.startTime = dayjs(data.startTime).format('YYYY-MM-DD')
        data.endTime = dayjs(data.endTime).format('YYYY-MM-DD')
        data.userId = userInfo.id
        data.userName = userInfo.realname
        data.duration = calcDayTimeStamp.value
        console.log(data);

        editRestRegister(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '编辑成功！')
            } else {
                toast.error(res.message)
            }
        })
    }
}

</script>

<style lang="scss" scoped></style>