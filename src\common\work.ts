/**
 * 常用服务
 * useful server
 */

const icon_prefix = '/static/index/128/'

/*
 */
export const us = {
  data: [
    {
      title: '记录营销动作',
      icon: icon_prefix + 'kehu.png',
      description: '记录营销动作',
      useCount: 10000,
      routeIndex: 'marketActionAddRecordMap',
      permission: '/service/actionCost/addMarketActionRecord'
    },
    {
      title: '自助查询',
      icon: icon_prefix + 'wendang.png',
      description: '自助查询',
      useCount: 10000,
      routeIndex: 'marketActionSelfQuery',
      permission: '/service/actionCost/selfQueryActionRecord'
    },
    {
      title: '我的活动地图',
      icon: icon_prefix + 'gongwen.png',
      description: '我的活动地图',
      useCount: 10000,
      routeIndex: 'marketActionActionMap',
      permission: '/service/actionCost/myRecordMap'
    },
    {
      title: '同行与分享',
      icon: icon_prefix + 'huiyi.png',
      description: '同行与分享',
      useCount: 10000,
      routeIndex: 'marketActionPartnerAndShare',
      permission: '/service/actionCost/partnerAndShare'
    },
    {
      title: '客户信息备忘录',
      icon: icon_prefix + 'wendang.png',
      description: '客户信息备忘录',
      useCount: 10000,
      routeIndex: 'marketActionVisitClientList',
      permission: '/service/actionCost/recordMemo'
    },
    {
      title: '阶段任务下达',
      icon: icon_prefix + 'richeng.png',
      description: '阶段任务下达',
      useCount: 10000,
      routeIndex: 'missionAssign',
      permission: '/service/actionCost/taskAllocation'
    },
    {
      title: '群的督查授权',
      icon: icon_prefix + 'tongxun.png',
      description: '群的督查授权',
      useCount: 10000,
      routeIndex: 'missionGroupAuth',
      permission: '/service/actionCost/auditAuth'
    },
    {
      title: '内勤审核',
      icon: icon_prefix + 'qingjia.png',
      description: '内勤审核',
      useCount: 10000,
      routeIndex: 'marketActionValidRecordList',
      permission: '/service/actionCost/recordAudit'
    },
    {
      title: '休息备案',
      icon: icon_prefix + 'qingjia1.png',
      description: '休息备案',
      useCount: 10000,
      routeIndex: 'marketActionRestRegister',
      permission: '/service/actionCost/visitRestFiling'
    },
    {
      title: '休息备案审核',
      icon: icon_prefix + 'qingjia1.png',
      description: '休息备案审核',
      useCount: 10000,
      routeIndex: 'marketActionRestRegisterValid',
      permission: '/service/actionCost/visitRestFiling'
    },
    {
      title: '我的报销',
      icon: icon_prefix + 'qingjia1.png',
      description: '我的报销',
      useCount: 10000,
      routeIndex: 'marketActionReimburseList',
      permission: '/service/actionCost/visitReimburse'
    },
    {
      title: '住宿补贴',
      icon: icon_prefix + 'qingjia1.png',
      description: '我的报销',
      useCount: 10000,
      routeIndex: 'marketActionLiveSubsidy',
      permission: '/service/actionCost/marketActionLiveSubsidy'
    }
    // {
    //   title: '测试页面',
    //   icon: icon_prefix + 'qingjia1.png',
    //   description: '测试页面',
    //   useCount: 10000,
    //   routeIndex: 'marketActionReimburse',
    // },
    // {
    //   title: '我的督查和审核',
    //   icon: icon_prefix + 'gongwen.png',
    //   description: '我的督查和审核',
    //   useCount: 10000,
    //   routeIndex: 'marketActionValidPeopleList',
    // },
    // {
    //   title: '测试添加记录',
    //   icon: icon_prefix + 'gongwen.png',
    //   description: '测试添加记录',
    //   useCount: 10000,
    //   routeIndex: 'marketActionAddRecord',
    // },
    // {
    //   title: '测试拜访记录',
    //   icon: icon_prefix + 'gongwen.png',
    //   description: '测试拜访记录',
    //   useCount: 10000,
    //   routeIndex: 'marketActionCheckSingleRecord',
    // },
    // {
    //   title: 'online',
    //   icon: icon_prefix + 'qingjia1.png',
    //   description: '请假申请',
    //   useCount: 10000,
    //   routeIndex: 'online',
    //   enabled: true,
    // },
    // {
    //   title: '组件示例',
    //   icon: icon_prefix + 'chuchai.png',
    //   description: '出差申请',
    //   useCount: 10000,
    //   routeIndex: 'demo',
    //   enabled: true,
    // },
    // {
    //   title: '通知公告',
    //   icon: icon_prefix + 'tongzhi.png',
    //   description: '查看企业对员工下发的通知公告',
    //   useCount: 10000,
    //   routeIndex: 'annotationList',
    //   enabled: true,
    // },
    // {
    //   title: '测试记录',
    //   icon: icon_prefix + 'richeng.png',
    //   description: '建立和查看个人工作安排',
    //   useCount: 10000,
    //   routeIndex: '',
    // },
    // {
    //   title: '考勤',
    //   icon: icon_prefix + 'kaoqin.png',
    //   description: '工作考勤',
    //   routeIndex: 'attendance',
    //   useCount: 10000,
    // },
    // {
    //   title: '内部邮件',
    //   icon: icon_prefix + 'youjian.png',
    //   description: '查看内部消息',
    //   useCount: 10000,
    //   dot: false,
    //   routeIndex: 'mailHome',
    // },
    // {
    //   title: '通讯录',
    //   icon: icon_prefix + 'tongxun.png',
    //   description: '查看组员',
    //   useCount: 10000,
    //   //routeIndex:'addressBook',
    //   routeIndex: 'levelAddressBook',
    // },
    // {
    //   title: '日报',
    //   icon: icon_prefix + 'richang.png',
    //   description: '记录每天的工作经验和心得',
    //   useCount: 1000,
    // },
    // {
    //   title: '周报',
    //   icon: icon_prefix + 'zhoubao.png',
    //   description: '总结每周的工作情况和下周计划',
    //   useCount: 10000,
    // },
  ],
}

/**
 * other server 其他服务
 */
export const os = {
  data: [
    // {
    //   title: '新闻中心',
    //   icon: icon_prefix + 'xinwen.png',
    //   description: '新闻中心',
    //   routeIndex: 'columnList',
    //   useCount: 10000,
    // },
    // {
    //   title: '文档中心',
    //   icon: icon_prefix + 'wendang.png',
    //   description: '文档中心',
    //   routeIndex: 'fileHome',
    //   useCount: 10000,
    // },
    // {
    //   title: '会议',
    //   icon: icon_prefix + 'huiyi.png',
    //   description: '会议',
    //   useCount: 10000,
    //   routeIndex: 'meeting',
    // },
    // {
    //   title: '任务中心',
    //   icon: icon_prefix + 'renwu.png',
    //   description: '任务中心',
    //   useCount: 10000,
    // },
    // {
    //   title: '合同',
    //   icon: icon_prefix + 'hetong.png',
    //   description: '合同',
    //   useCount: 10000,
    // },
    // // #ifndef MP-WEIXIN
    // {
    //   title: '聊天',
    //   icon: icon_prefix + 'kehu.png',
    //   description: '聊天',
    //   routeIndex: 'chathome',
    // },
    // // #endif
  ],
}