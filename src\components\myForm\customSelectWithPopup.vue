<template>
    <wd-cell :title="''" :title-width="'0px'" :prop="prop" use-default-slot>
        <view class="flex justify-between">
            <view class="mr2">{{ `` }}</view>
            <wd-button type="warning" custom-class="" @click="show = true" size="small" :disabled="disabled">{{ label
                }}</wd-button>
        </view>
        <wd-popup v-model="show" custom-style="border-radius:32rpx;" @close="show = false" closable>
            <view class="w75vw h60vh py4 pr4">
                <view class="text-left font-bold font-size-5 pl4">{{ label }}</view>
                <view class="flex items-center">
                    <wd-search v-model="searchValue" @search="search" @clear="searchValue = ''" :maxlength="50"
                        custom-class="w70% pl1" :placeholder="'输入名称搜索'" placeholder-left hide-cancel />
                    <wd-button type="success" custom-class="" size="small" :disabled="disabled"
                        @click="toAddNew">新建</wd-button>
                </view>
                <view>
                    <wd-cell-group :border="true">
                        <wd-cell :title="item.label" :value="item.value" v-for="(item, index) in ListData" clickable />
                    </wd-cell-group>
                </view>
            </view>
        </wd-popup>
    </wd-cell>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()
const show = ref(false)
const searchValue = ref('')
const ListData = ref([
    {
        label: '经销商1',
        value: '1'
    },
    {
        label: '经销商2',
        value: '2'
    }
])

const toAddNew = () => {
    console.log('新建');
}

const search = () => {

}

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Array,
        default: []
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const toSelPartner = () => {
    console.log(props.defaultData);

    router.push({
        name: 'marketActionChooseGroupFindAll',
        params: {
            defaultData: JSON.stringify(props.defaultData),
            name: props.name
        }
    })
}

</script>

<style lang="scss" scoped></style>