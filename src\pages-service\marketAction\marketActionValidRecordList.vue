<route lang="json5" type="page">
  {
    layout: 'default',
    style: {
      navigationBarTitleText: '内勤审核',
      navigationStyle: 'custom',
    },
  }
</route>

<template>
  <PageLayout backRouteName="index" navTitle="内勤审核" routeMethod="replaceAll">
    <view class="bg-#ffffff pb3">
      <wd-segmented :options="segList" v-model:value="segIndex" @change="changeSeg"></wd-segmented>
      <view class="flex flex-items-center w100%">
        <wd-picker :columns="groupPickerColumns" v-model="groupPicker" @confirm="confirmGroupPicker" use-default-slot
          custom-class="w50%">
          <wd-button icon="search" plain custom-class="w100%">
            {{groupPickerColumns.length != 0 ? groupPickerColumns.find(item => item.value == groupPicker).label : ''}}
          </wd-button>
        </wd-picker>
        <wd-calendar type="month" v-model="validDate" use-default-slot @confirm="confirmValidDate" custom-class="w50%">
          <wd-button icon="calendar" plain custom-class="w100%">
            {{ dayjs(validDate).format('YYYY-MM') }}
          </wd-button>
        </wd-calendar>
      </view>
      <view v-if="segIndex == '未审核动作列表'">
        <view class="ml3 mr3 mt3" v-if="validTable0.length != 0">
          <wd-table :data="validTable0" :height="'70vh'" :fixed-header="true">
            <wd-table-col prop="userName" label="经理名称" :width="80" fixed></wd-table-col>
            <wd-table-col prop="firstVisitNum" label="首次拜访" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.firstVisitNum, row)">
                  {{ row.firstVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="undealVisitNum" label="未重拜" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.undealVisitNum, row)">
                  {{ row.undealVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="dealVisitNum" label="已重拜" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.dealVisitNum, row)">
                  {{ row.dealVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="biologicalTestsNum" label="生测实验" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.biologicalTestsNum, row)">
                  {{ row.biologicalTestsNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="modelFieldNum" label="示范田" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.modelFieldNum, row)">
                  {{ row.modelFieldNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="orderMeetingNum" label="订货会" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.orderMeetingNum, row)">
                  {{ row.orderMeetingNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="observationMeetingNum" label="观摩会" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.observationMeetingNum, row)">
                  {{ row.observationMeetingNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="noteNum" label="工作笔记" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.noteNum, row)">
                  {{ row.noteNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="pullTrainingNum" label="拉练" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.pullTrainingNum, row)">
                  {{ row.pullTrainingNum }}
                </wd-button>
              </template>
            </wd-table-col>
          </wd-table>
        </view>
        <wd-status-tip v-if="validTable0.length == 0" image="search" tip="无结果" />
      </view>
      <view v-else-if="segIndex == '已审核动作列表'">
        <view class="ml3 mr3 mt3" v-if="validTable1.length != 0">
          <wd-table :data="validTable1" :height="'70vh'" :fixed-header="true">
            <wd-table-col prop="userName" label="经理名称" :width="80" fixed></wd-table-col>
            <wd-table-col prop="firstVisitNum" label="首次拜访" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.firstVisitNum, row)">
                  {{ row.firstVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="undealVisitNum" label="未重拜" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.undealVisitNum, row)">
                  {{ row.undealVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="dealVisitNum" label="已重拜" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.dealVisitNum, row)">
                  {{ row.dealVisitNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="biologicalTestsNum" label="生测实验" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.biologicalTestsNum, row)">
                  {{ row.biologicalTestsNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="modelFieldNum" label="示范田" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.modelFieldNum, row)">
                  {{ row.modelFieldNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="orderMeetingNum" label="订货会" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.orderMeetingNum, row)">
                  {{ row.orderMeetingNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="observationMeetingNum" label="观摩会" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.observationMeetingNum, row)">
                  {{ row.observationMeetingNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="noteNum" label="工作笔记" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.noteNum, row)">
                  {{ row.noteNum }}
                </wd-button>
              </template>
            </wd-table-col>
            <wd-table-col prop="pullTrainingNum" label="拉练" :width="colWidth">
              <template #value="{ row }">
                <wd-button type="text" @click="toMySearch(custom_visitType.pullTrainingNum, row)">
                  {{ row.pullTrainingNum }}
                </wd-button>
              </template>
            </wd-table-col>
          </wd-table>
        </view>
        <wd-status-tip v-if="validTable1.length == 0" image="search" tip="无结果" />
      </view>
    </view>
  </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
const toast = useToast()
import { useRouter } from '@/plugin/uni-mini-router'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { getMyGroupList, getGroupMarketActionStatics, getMyReviewGroupList } from '@/service/marketAction/marketAction'

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const router = useRouter()

const colWidth = ref(50)

const custom_visitType = {
  firstVisitNum: 1,
  undealVisitNum: 2,
  dealVisitNum: 3,
  biologicalTestsNum: 4,
  modelFieldNum: 5,
  orderMeetingNum: 6,
  observationMeetingNum: 7,
  noteNum: 8,
  pullTrainingNum: 9
}

const groupPicker = ref(0)
const groupPickerColumns = ref([])
const confirmGroupPicker = (e) => {
  console.log(e);
  getTable()
}

const validDate = ref<number>(Date.now())
const confirmValidDate = (e) => {
  console.log(e);
  getTable()
}

const segIndex = ref('未审核动作列表')
const segList = ref<string[]>(['未审核动作列表', '已审核动作列表'])
const changeSeg = (e) => { }

// 表格
const validTable0 = ref([])
const validTable0Click = (e) => {
  console.log(e);
}

const validTable1 = ref([])
const validTable1Click = (e) => {
  console.log(e);
}

onLoad(() => {
  // 查询群组
  getMyReviewGroupList({
    znkqUserId: userInfo.znkqId
  }).then((res: any) => {
    if (res.code == 200) {
      groupPicker.value = Number(res.result[0].id)
      groupPickerColumns.value = res.result.map(item => ({
        value: Number(item.id),
        label: item.name
      }))
      // 查询表格
      getTable()
    } else {
      toast.error(res.message)
    }
  })
})

// 查询表格
const getTable = () => {
  getGroupMarketActionStatics({
    date: dayjs(validDate.value).format('YYYY-MM'),
    groupId: groupPicker.value
  }).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      validTable0.value = res.result.notAuditDataList
      validTable1.value = res.result.passDataList
    }
  })
}

const toMySearch = (visitType, row) => {
  console.log(visitType, row);
  router.push({
    path: '/pages-service/marketAction/marketActionValidPeopleList',
    query: {
      groupId: String(groupPicker.value),
      userId: row.userId,
      userName: row.userName,
      date: String(validDate.value),
      visitType: visitType,
      auditState: segIndex.value == '未审核动作列表' ? '0' : '1'
    }
  })
}
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
  display: none;
}
</style>