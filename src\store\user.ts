import { defineStore } from 'pinia'
import { ref } from 'vue'
import { queryZnkqUserInfo } from '@/service/api'

const initState = {
  token: '',
  userid: '',
  username: '',
  realname: '',
  welcome: '',
  avatar: '',
  tenantId: 1003,
  phone: '',
  email: '',
  sex: 1,
  znkqId: -1,
  // 本地存储时间
  localStorageTime: 0,
  // 接口返回的参数id(同userId)
  id: ''
}

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initState })
    const setUserInfo = (val: IUserInfo) => {
      userInfo.value = val
    }
    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    const editUserInfo = (options) => {
      userInfo.value = { ...userInfo.value, ...options }
    }
    // 一般没有reset需求，不需要的可以删除
    const reset = () => {
      userInfo.value = { ...initState }
    }
    const isLogined = computed(() => !!userInfo.value.token)

    const getZnkqId = () => {
      queryZnkqUserInfo().then((res: any) => {
        // console.log(res);
        if (res.code == 200) {
          userInfo.value = { ...userInfo.value, ...{ znkqId: res.result.id } }
        }
      })
    }

    return {
      userInfo,
      setUserInfo,
      clearUserInfo,
      isLogined,
      editUserInfo,
      reset,
      getZnkqId
    }
  },
  {
    // 如果需要持久化就写 true, 不需要持久化就写 false（或者去掉这个配置项）
    persist: true,
  },
)
