<route lang="json5" type="home">
  {
    layout: 'default',
    style: {
      navigationStyle: 'custom',
      navigationBarTitleText: '首页',
    },
  }
</route>
<template>
  <PageLayout :navbarShow="false" tabbarIndexProp="/home">
    <view class="min-h-screen bg-purple-50 pb-24">

      <index v-if="activeTab == '/home'" :menusProp="services"></index>
      <!-- <service v-if="activeTab == '/service'" :menusProp="services" :catIndexProp="catIndex"></service> -->
      <message v-if="activeTab == '/message'"></message>
      <user v-if="activeTab == '/mine'"></user>

      <!-- 底部可滚动 Tab 栏 -->
      <view class="fixed bottom-0 left-0 right-0 z-50 p-4 pb-3">
        <view class="bg-white rounded-3xl shadow-lg border border-gray-100">
          <scroll-view v-if="menus.length > 5" class="whitespace-nowrap" :scroll-x="true" :show-scrollbar="false">
            <view class="flex px-6 min-w-full">
              <view
                class="flex flex-col items-center justify-center min-w-20 h-16 transition-all duration-300 mx-2 relative"
                v-for="(tab, index) in menus" :key="index"
                :class="{ 'transform scale-105': activeTab === tab.menuPerms }" @tap="onTabTap(tab)">
                <image class="w-6 h-6 mb-1" :src="tab.selectUrl" mode="aspectFit"></image>
                <text class="text-xs whitespace-nowrap"
                  :class="activeTab === tab.menuPerms ? 'text-purple-600 font-semibold' : 'text-gray-600'">{{ tab.name
                  }}</text>
                <view v-if="activeTab === tab.menuPerms"
                  class="absolute bottom-3 w-8 h-1 bg-purple-600 rounded-full bottom-1">
                </view>
              </view>
            </view>
          </scroll-view>
          <view v-else class="flex px-6">
            <view class="flex-1 flex flex-col items-center justify-center h-16 transition-all duration-300 relative"
              v-for="(tab, index) in menus" :key="index" :class="{ 'transform scale-105': activeTab === tab.menuPerms }"
              @tap="onTabTap(tab)">
              <image class="w-6 h-6 mb-1" :src="tab.selectUrl" mode="aspectFit"></image>
              <text class="text-xs whitespace-nowrap"
                :class="activeTab === tab.menuPerms ? 'text-purple-600 font-semibold' : 'text-gray-600'">{{ tab.name
                }}</text>
              <view v-if="activeTab === tab.menuPerms" class="absolute w-8 h-1 bg-purple-600 rounded-full bottom-1">
              </view>
            </view>
          </view>
        </view>
      </view>


    </view>
  </PageLayout>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
// 获取当前运行平台

import index from './components/index.vue'
import user from './components/user.vue'
import service from './components/service.vue'
import message from './components/message.vue'

import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useGlobalStore } from '@/store'

const globalData = getApp().globalData
const { systemInfo, navHeight } = globalData
const { statusBarHeight } = systemInfo

import { useToast, useMessage, useNotify } from 'wot-design-uni'
const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store/user'
const userStore = useUserStore()



const { userInfo } = userStore
const useGlobal = useGlobalStore()

// 底部菜单
const menus = ref([])
// 首页服务大类
const services = ref([])
const catIndex = ref()

// 选择的tab
const activeTab = ref('/home')
const onTabTap = (tab) => {
  activeTab.value = tab.menuPerms;
  // console.log('切换到tab:', menus[index]);
}

onShow(() => {
  // console.log('onload-pagelayout', props.tabbarIndexProp);

  setTimeout(() => {
    // 初始化获取菜单
    menus.value = useGlobal.global.menus
    console.log(menus.value);

    // 首页服务功能选项
    // services.value = menus.value.find(item => item.menuPerms == '/service')?.children
    // console.log(services.value);
    services.value = [{
      name: "记录营销动作",
      menuPerms: "/service/actionCost/addMarketActionRecord",
      selectUrl: "https://qn.gxcf.ltd/upload/2025/07/17/yingxiaodongzuo_1752736342793.png",
      to: '/pages-service/marketAction/marketActionAddRecordMap'
    },
    ]


  }, 500);

})

onLoad(() => {
})

const goService = (item) => {
  console.log(item);
  router.push({
    path: '/pages/service/service',
    query: {
      serviceIndex: item.serviceIndex
    }
  })
}

uni.$on('changeTab', (param) => {
  console.log(param);
  activeTab.value = param.tab
  catIndex.value = param.service;
})



</script>