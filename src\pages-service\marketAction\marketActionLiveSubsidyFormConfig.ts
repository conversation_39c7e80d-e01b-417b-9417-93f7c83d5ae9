import { FormRules } from "wot-design-uni/components/wd-form/types"

const formConfig = ref([
    {
        title: '',
        form: [
            {
                type: 'picker',
                label: '人员类型',
                name: 'userStayType',
                columns: [],
            },
            {
                type: 'picker',
                label: '住宿类型',
                name: 'stayType',
                columns: [],
            },
            {
                type: 'picker',
                label: '按照群组或人员',
                name: 'addByGroupOrPeople',
                columns: [{
                    value: 1,
                    label: '群组'
                }, {
                    value: 2,
                    label: '人员'
                }],
            },
            {
                type: 'selectGroups',
                label: '群组',
                name: 'custom_selectGroups',
                showRules: {
                    by: 'addByGroupOrPeople',
                    arr: [1]
                }
            },
            // {
            //     type: 'picker',
            //     label: '群组',
            //     name: 'groupId',
            //     columns: [],
            //     showRules: {
            //         by: 'addByGroupOrPeople',
            //         arr: [1]
            //     }
            // },
            // {
            //     type: 'picker',
            //     label: '人员',
            //     name: 'userId',
            //     columns: [],
            //     showRules: {
            //         by: 'addByGroupOrPeople',
            //         arr: [2]
            //     }
            // },
            {
                type: 'selectPeoples',
                label: '人员',
                name: 'custom_selectPeoples',
                showRules: {
                    by: 'addByGroupOrPeople',
                    arr: [2]
                }
            },
            {
                type: 'input',
                label: '补贴标准',
                textType: 'digit',
                name: 'subsidyStandard',
                placeholder: '请输入补贴标准(元)'
            }
        ]
    }
])

const rules: FormRules = {
    userStayType: [
        {
            required: true,
            message: '请选择人员类型',
            trigger: ['blur', 'change']
        }
    ],
    stayType: [
        {
            required: true,
            message: '请选择住宿类型',
            trigger: ['blur', 'change']
        }
    ],
    addByGroupOrPeople: [
        {
            required: true,
            message: '请选择',
            trigger: ['blur', 'change']
        }
    ],
    groupId: [
        {
            required: true,
            message: '请选择群组',
            trigger: ['blur', 'change']
        }
    ],
    userId: [
        {
            required: true,
            message: '请选择人员',
            trigger: ['blur', 'change']
        }
    ],
    subsidyStandard: [
        {
            required: true,
            message: '请选择补贴标准',
            trigger: ['blur', 'change']
        }
    ],
    custom_selectPeoples: [
        {
            required: true,
            validator: (value) => {
                console.log(value);

                if (!value) {
                    return false
                } else if (typeof value == 'object' && Object.keys(value).length !== 0) {
                    return true
                } else if (typeof value == 'object' && value.length == 0) {
                    return false
                } else {
                    return true;
                }
            },
            message: '请选择人员',
            trigger: ['blur', 'change'],
        }
    ],
    custom_selectGroups:[
        {
            required: true,
            validator: (value) => {
                console.log(value);

                if (!value) {
                    return false
                } else if (typeof value == 'object' && Object.keys(value).length !== 0) {
                    return true
                } else if (typeof value == 'object' && value.length == 0) {
                    return false
                } else {
                    return true;
                }
            },
            message: '请选择群组',
            trigger: ['blur', 'change'],
        }
    ]
}

export { formConfig, rules };