<template>
    <view class="">
        <wd-cell :title="label" :title-width="'100%'" :prop="prop">
            <template #label>
                <view class="mt2 mb2">
                    <wd-upload :file-list="fileList" image-mode="aspectFill" :action="action" @change="handleChange"></wd-upload>
                </view>
            </template>
        </wd-cell>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: ''
    },
    showCopy: {
        type: Boolean,
        default: false
    },
    defaultData: {
        type: Object,
        default: () => { }
    }
})

const copy = () => {

}

const toSelLocation = () => {
    router.push({
        name: 'selectActionLocationByMap',
        params: {
            ...props.defaultData,
            name: props.name
        }
    })
}

const fileList = ref<any[]>([
  {
    url: 'https://img12.360buyimg.com//n0/jfs/t1/29118/6/4823/55969/5c35c16bE7c262192/c9fdecec4b419355.jpg'
  }
])

const action: string = 'https://mockapi.eolink.com/zhTuw2P8c29bc981a741931bdd86eb04dc1e8fd64865cb5/upload'

function handleChange({ fileList: files }) {
  fileList.value = files
}

</script>

<style lang="scss" scoped></style>