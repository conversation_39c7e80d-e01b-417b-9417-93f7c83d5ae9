<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '住宿补贴',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout backRouteName="index" navTitle="住宿补贴" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white ml3 mt3">
                <wd-search v-model="searchName" @search="getSubsidy" @clear="getSubsidy" hide-cancel custom-class="w70%"
                    placeholder="输入人员名称搜索" />
                <wd-icon name="add-circle" color="#0083ff" size="28px" custom-class="ml3" @click="toAdd" />
            </view>
            <view class="ml3 mr3 mb3 mt2">
                <wd-table :data="subsidy" :height="'70vh'" @row-click="subsidyClick" :fixed-header="false">
                    <wd-table-col prop="userName" label="经理名称" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="userStayType_dictText" label="人员类型" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="stayType_dictText" label="住宿类型" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="subsidyStandard" label="补贴标准" :width="80"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="60">
                        <template #value="{ row }">
                            <wd-button type="error" size="small" custom-class="" @click.stop="toDel(row)">删除</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="subsidyPageNo"
                    :pageSize="subsidyPageSize" :total="subsidyTotal" @change="subsidyPageChange"></wd-pagination>
                <wd-status-tip v-if="subsidy.length == 0" image="search" tip="当前搜索无结果" />
            </view>
        </view>
    </PageLayout>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { getStayInfo, delStayInfo } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const colWidth = ref(100)

// 按名称搜索
const searchName = ref('')

// 表格Subsidy
const subsidy = ref([])
const subsidyClick = (e) => {
}
// 分页
const subsidyPageNo = ref<number>(1)
const subsidyPageSize = ref<number>(10)
const subsidyTotal = ref<number>(subsidy.value.length)
const subsidyPageChange = (e) => {
    subsidyPageNo.value = e.value
    getSubsidy()
}

// 获取列表
const getSubsidy = () => {
    getStayInfo({
        pageNo: subsidyPageNo.value,
        pageSize: subsidyPageSize.value,
        userName: searchName.value,
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            subsidy.value = res.result.records
        }
    })
}

const toAdd = (e) => {
    router.push({
        path: '/pages-service/marketAction/marketActionLiveSubsidyAdd',
        query: {
            action: 'add'
        }
    })
}

const toDel = (e) => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delStayInfo({
                id: e.id
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    getSubsidy()
                }
            })
        })
}

onLoad(() => {
    getSubsidy()
    uni.$on('refreshList', (e) => {
        toast.success(e)
        getSubsidy()
    })
})
onUnload(() => {
    uni.$off('refreshList')
})

</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>
