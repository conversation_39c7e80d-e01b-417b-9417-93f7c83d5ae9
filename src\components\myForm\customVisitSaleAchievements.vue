<template>
    <view class="flex flex-col items-center">
        <wd-table :data="dataList" :height="400" custom-class="p2">
            <wd-table-col prop="productName" label="商品名" :width="180"></wd-table-col>
            <wd-table-col prop="productUnit_dictText" label="单位" :width="colWidth"></wd-table-col>
            <wd-table-col prop="productNum" label="数量" :width="colWidth"></wd-table-col>
            <wd-table-col prop="" label="操作" :width="colWidth">
                <template #value="{ row }">
                    <wd-button type="error" size="small" custom-class="" :disabled="disabled"
                        @click.stop="toDel(row)">删除</wd-button>
                </template>
            </wd-table-col>
        </wd-table>
        <wd-button type="warning" custom-class="mt1 mb2 w60%" @click="showPopup" :disabled="disabled">添加销售战绩</wd-button>
        <wd-popup v-model="showAdd" custom-style="border-radius:32rpx;" @close="showAdd = false">
            <view class="p4 w80vw h60vh bg-#ffffff block">
                <view class="font-size-4 font-bold">商品名</view>
                <wd-input type="text" v-model="productName" placeholder="请输入商品名" custom-class="mt1 mb2" />
                <view class="font-size-4 font-bold">商品数量</view>
                <!-- <wd-input type="number" v-model="productNum" placeholder="请输入数量" custom-class="mt1"/> -->
                <wd-input-number v-model="productNum" custom-class="mt1" />
                <wd-radio-group v-model="productUnit" cell shape="button">
                    <wd-radio value="件">件</wd-radio>
                    <wd-radio value="包">包</wd-radio>
                    <wd-radio value="袋">袋</wd-radio>
                    <wd-radio value="箱">箱</wd-radio>
                    <wd-radio value="桶">桶</wd-radio>
                    <wd-radio value="瓶">瓶</wd-radio>
                    <wd-radio value="克">克</wd-radio>
                    <wd-radio value="斤">斤</wd-radio>
                    <wd-radio value="公斤">公斤</wd-radio>
                    <wd-radio value="千克">千克</wd-radio>
                </wd-radio-group>
                <wd-button type="primary" custom-class="mt3 w100%" @click="addOne">添加</wd-button>
            </view>
        </wd-popup>
    </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'
import { getDict } from '@/service/index/foo'
const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Array,
        default: []
    },
    disabled: {
        type: Boolean,
        default: false
    }
})
const productUnitList = ref([])
getDict('productUnit').then((res: any) => {
    productUnitList.value = res.result.map(item => ({
        value: Number(item.value),
        label: item.label
    }))
})

const colWidth = ref(70)

const router = useRouter()
const toast = useToast()
const dataList = ref([])

const showAdd = ref(false)
const productName = ref('')
const productUnit = ref('')
const productNum = ref(1)
// const productUnitColumns = ref(['件', '包', '瓶', '克', '斤'])
// const confirmProductUnit = (e) => {
//     console.log(e);
// }

const showPopup = () => {
    productName.value = ''
    productNum.value = 1
    productUnit.value = ''
    showAdd.value = true
}

const addOne = () => {
    if (!(productName.value && productUnit.value && productNum.value)) {
        toast.error('请填写完整信息')
        return
    }
    console.log(productUnitList.value);

    let data = {
        productName: productName.value,
        productUnit: productUnitList.value.find(item => item.label == productUnit.value)?.value || '',
        productUnit_dictText: productUnit.value,
        productNum: productNum.value
    }
    console.log(data);
    dataList.value.push(data)
    showAdd.value = false
    uni.$emit('setSaleAchievements', dataList.value)
}

// #ifdef H5
onLoad(() => {
    console.log('vsa', props.defaultData);
    dataList.value = props.defaultData
})
// #endif
// #ifdef MP-WEIXIN
onReady(() => {
    console.log('vsa', props.defaultData);
    dataList.value = props.defaultData
})
// #endif


const toDel = (e) => {
    console.log(e);
    dataList.value = dataList.value.filter(item => item.productName != e.productName)
    uni.$emit('setSaleAchievements', dataList.value)
}

</script>

<style lang="scss" scoped></style>