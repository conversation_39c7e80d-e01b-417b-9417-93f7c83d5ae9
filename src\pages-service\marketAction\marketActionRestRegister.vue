<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '我的休息备案',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout backRouteName="index" navTitle="我的休息备案" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white ml3 mt3">
                <wd-picker :columns="actionTypeColumns" v-model="actionType" @confirm="confirmActionType"
                    use-default-slot>
                    <wd-button icon="search" plain>
                        {{actionTypeColumns.find(item => Number(item.value) == actionType).label}}
                    </wd-button>
                </wd-picker>
                <wd-icon name="add-circle" color="#0083ff" size="28px" custom-class="ml3" @click="toAdd" />
            </view>
            <view class="ml3 mr3 mb3 mt2">
                <wd-table :data="restTable" :height="'70vh'" @row-click="restTableClick" :fixed-header="false">
                    <wd-table-col prop="startTime" label="开始时间" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="endTime" label="结束时间" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="duration" label="时长(天)" :width="80">
                        <template #value="{ row }">
                            <view>
                                {{ row.duration / (1000 * 3600 * 24) }}
                            </view>
                        </template>
                    </wd-table-col>
                    <wd-table-col prop="reviewStatus_dictText" label="状态" :width="90"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="120">
                        <template #value="{ row }">
                            <wd-button size="small" custom-class="" v-if="row.reviewStatus == 0"
                                @click.stop="toEdit(row)">修改</wd-button>
                            <wd-button type="error" size="small" v-if="row.reviewStatus == 0" custom-class=""
                                @click.stop="toDel(row)">删除</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="restTablePageNo"
                    :pageSize="restTablePageSize" :total="restTableTotal" @change="restTablePageChange"></wd-pagination>
                <wd-status-tip v-if="restTable.length == 0" image="search" tip="当前搜索无结果" />
            </view>
        </view>
    </PageLayout>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { getRestRegister, delRestRegister } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const colWidth = ref(100)
// (筛选)
const actionType = ref(-1)
const actionTypeColumns = ref([
    { value: '-1', label: '审核状态' },
    { value: '0', label: '未审核' },
    { value: '1', label: '已通过' },
    { value: '2', label: '未通过' }
])
const confirmActionType = (e) => {
    // console.log(e);
    actionType.value = e.value
    getRestTable()
}
// 表格
const restTable = ref([])
const restTableClick = (e) => {
    console.log(e);
    console.log(restTable.value);

    router.push({
        path: '/pages-service/marketAction/marketActionRestRegisterAdd',
        query: {
            action: 'check',
            id: restTable.value[e.rowIndex].id
        }
    })
}
// 分页
const restTablePageNo = ref<number>(1)
const restTablePageSize = ref<number>(10)
const restTableTotal = ref<number>(restTable.value.length)
const restTablePageChange = (e) => {
    restTablePageNo.value = e.value
    getRestTable()
}

const getRestTable = () => {
    let data = {
        pageNo: restTablePageNo.value,
        pageSize: restTablePageSize.value,
        reviewStatus: actionType.value == -1 ? '' : actionType.value
    }
    console.log(data);
    // return
    getRestRegister({
        pageNo: restTablePageNo.value,
        pageSize: restTablePageSize.value,
        reviewStatus: actionType.value == -1 ? '' : actionType.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            restTable.value = res.result.records
        }
    })
}

const toAdd = (e) => {
    router.push({
        path: '/pages-service/marketAction/marketActionRestRegisterAdd',
        query: {
            action: 'add'
        }
    })
}

const toEdit = (e) => {
    console.log(e);
    router.push({
        path: '/pages-service/marketAction/marketActionRestRegisterAdd',
        query: {
            action: 'edit',
            id: e.id
        }
    })
}

const toDel = (e) => {
    message
        .confirm({
            title: '提示',
            msg: '确定删除吗？',
        })
        .then(() => {
            delRestRegister({
                id: e.id
            }).then((res: any) => {
                console.log(res);
                if (res.code == 200) {
                    getRestTable()
                }
            })
        })
}

onLoad(() => {
    getRestTable()
    uni.$on('refreshList', (e) => {
        toast.success(e)
        getRestTable()
    })
})
onUnload(() => {
    uni.$off('refreshList')
})
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}
</style>