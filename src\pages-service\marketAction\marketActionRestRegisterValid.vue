<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '我的休息备案审核',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout backRouteName="index" navTitle="我的休息备案审核" routeMethod="replaceAll">
        <view class="bg-#ffffff">
            <view class="flex flex-items-center w100% bg-white ml3 mt3">
                <wd-search v-model="searchName" @search="searchByName" hide-cancel custom-class="w50%"
                    placeholder="输入市场经理搜索" @clear="searchByName" />
                <wd-picker :columns="actionTypeColumns" v-model="actionType" @confirm="confirmActionType"
                    use-default-slot>
                    <wd-button icon="search" plain>
                        {{actionTypeColumns.find(item => Number(item.value) == actionType).label}}
                    </wd-button>
                </wd-picker>
            </view>
            <view class="ml3 mr3 mb3 mt2">
                <wd-table :data="restTable" :height="'70vh'" @row-click="restTableClick" :fixed-header="false">
                    <wd-table-col prop="userName" label="市场经理" :width="colWidth"></wd-table-col>
                    <wd-table-col prop="duration" label="时长" :width="50">
                        <template #value="{ row }">
                            <view>
                                {{ row.duration / (1000 * 3600 * 24) }}天
                            </view>
                        </template>
                    </wd-table-col>
                    <wd-table-col prop="remark" label="备注" :width="80">
                        <template #value="{ row }">
                            <view class="ellipsis" @click.stop="showText(row.remark)">
                                <text>{{ row.remark }}</text>
                            </view>
                        </template>
                    </wd-table-col>
                    <wd-table-col prop="reviewStatus_dictText" label="状态" :width="90"></wd-table-col>
                    <wd-table-col prop="" label="操作" :width="70">
                        <template #value="{ row }">
                            <wd-button type="warning" size="small" custom-class="" v-if="row.reviewStatus == 0"
                                @click.stop="toValid(row)">审核</wd-button>
                        </template>
                    </wd-table-col>
                </wd-table>
                <wd-pagination custom-style="border: 1px solid #ececec;border-top:none" v-model="restTablePageNo"
                    :pageSize="restTablePageSize" :total="restTableTotal" @change="restTablePageChange"></wd-pagination>
                <wd-status-tip v-if="restTable.length == 0" image="search" tip="当前搜索无结果" />
            </view>
        </view>
        <wd-message-box selector="validBox">
            <!-- <wd-textarea v-model="validText" placeholder="请填写审核意见" /> -->
            <!-- <wd-divider></wd-divider> -->
            <wd-radio-group v-model="validPass" inline shape="dot" checked-color="#4D80F0" size="large">
                <wd-radio value="2">不通过</wd-radio>
                <wd-radio value="1">通过</wd-radio>
            </wd-radio-group>
        </wd-message-box>
    </PageLayout>
</template>
<script lang="ts" setup>
import { onShow, onHide, onLoad, onReady, onUnload } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { queryReviewPageList, validRestFiling } from '@/service/marketAction/marketAction';

const toast = useToast()
const message = useMessage()

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

const colWidth = ref(100)

// 搜索
const searchName = ref('')
const searchByName = (e) => {
    console.log(e);
    getRestTable()
}

// (筛选)
const actionType = ref(-1)
const actionTypeColumns = ref([
    { value: '-1', label: '审核状态' },
    { value: '0', label: '未审核' },
    { value: '1', label: '已通过' },
    { value: '2', label: '未通过' }
])
const confirmActionType = (e) => {
    // console.log(e);
    actionType.value = e.value
    getRestTable()
}
// 表格
const restTable = ref([])
const restTableClick = (e) => {
    console.log(e);
    console.log(restTable.value);

    // router.push({
    //     path: '/pages-service/mission/missionAssignAdd',
    //     query: {
    //         action: 'check',
    //         id: restTable.value[e.rowIndex].id
    //     }
    // })
}
// 分页
const restTablePageNo = ref<number>(1)
const restTablePageSize = ref<number>(10)
const restTableTotal = ref<number>(restTable.value.length)
const restTablePageChange = (e) => {
    restTablePageNo.value = e.value
    getRestTable()
}

const getRestTable = () => {
    // let data = {
    //     pageNo: restTablePageNo.value,
    //     pageSize: restTablePageSize.value,
    //     reviewStatus: actionType.value == -1 ? '' : actionType.value,
    //     reviewUserName: searchName.value
    // }
    // console.log(data);
    // return
    queryReviewPageList({
        pageNo: restTablePageNo.value,
        pageSize: restTablePageSize.value,
        reviewStatus: actionType.value == -1 ? '' : actionType.value,
        userName: searchName.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            restTable.value = res.result.records
        }
    })
}

const validBox = useMessage('validBox')
const validPass = ref(-1)
// const validText = ref('')
const toValid = (row) => {
    console.log(row);

    validBox.confirm({
        title: '审核意见'
    }).then(() => {
        if (validPass.value == -1) {
            toast.info('请选择是否通过')
            return
        }
        // console.log(validText.value, validPass.value);
        validRestFiling({
            id: row.id,
            reviewStatus: validPass.value,
        }).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                toast.success('审核成功')
                getRestTable()
            } else {
                toast.error(res.message)
            }
        })
    }).catch((error) => {
        console.log(error)
    })
}

const showText = (e) => {
    message.alert(e)
}

onLoad(() => {
    getRestTable()
})
onUnload(() => {
})
</script>

<style lang="scss" scoped>
::v-deep .uni-scroll-view::-webkit-scrollbar {
    display: none;
}

.ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>