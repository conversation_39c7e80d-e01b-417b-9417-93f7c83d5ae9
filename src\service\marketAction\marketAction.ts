import { http } from '@/utils/http'



// 查询轨迹
export const queryUserTrajectoryByDate = (params) => http.get("/action/userTrajectory/queryUserTrajectoryByDate", params);
// 查询营销动作次数
export const queryVisitRecordsNumber = (params) => http.get("/action/visitRecords/queryVisitRecordsNumber", params);



// 添加记录
export const addMarketActionRecord = (params) => http.post("/action/visitRecords/add", params);
// 编辑记录
export const editMarketActionRecord = (params) => http.post("/action/visitRecords/edit", params);
// 最终提交
export const finalSubmission = (params) => http.post("/jeecg-action/action/visitRecords/finalSubmission", params);
// 删除记录
export const delMarketActionRecord = (params) => http.delete("/action/visitRecords/delete", params);
// 审核id 意见 状态
export const auditVisit = (params) => http.post("/jeecg-action/action/visitRecords/auditVisit", params);
// 查询站点促销列表
export const getVisitSalesAchievement = (params) => http.get("/jeecg-action/action/visitSalesAchievement/queryByRecordId", params);
// 查询visitTypeInfo   
export const getVisitTypeInfoById = (params) => http.get("/jeecg-action/action/visitTypeInfo/queryByRecordId", params);



// 获取已有客户信息
export const getExistClients = (params) => http.get("/jeecg-action/action/visitClients/getMyVisitingClients", params);
// 查询常用同行人 pageSize
export const getComUsedPartners = (params) => http.get("/jeecg-action/action/visitPartner/list", params);
// 检查常用重复
export const checkComUsedRepeatByUserIds = (params) => http.get("/jeecg-action/action/visitPartner/checkRepeatByUserIds", params);
// 设置常用同行人
export const addComUsedPartners = (params) => http.post("/jeecg-action/action/visitPartner/add", params);
// 删除常用同行人
export const delComUsedPartners = (params) => http.delete("/jeecg-action/action/visitPartner/delete", params);
// 批量删除
export const delComUsedPartnersBatch = (params) => http.delete("/jeecg-action/action/visitPartner/deleteBatch", params);
// 查询所有同行人(?)
export const getUserTenantPageList = (params) => http.get("/sys/tenant/getUserTenantPageList", params);




// id查询记录 
export const getVisitRecordById = (params) => http.get("/jeecg-action/action/visitRecords/queryById", params);
// 我已分享的人员列表
export const getMyShareList = (params) => http.get("/jeecg-action/action/visitRecordsShare/getMyShareList", params);
// 添加分享人
export const addSharePeople = (params) => http.post("/jeecg-action/action/visitRecordsShare/add", params);
// 删除分享人
export const delSharePeople = (params) => http.delete("/jeecg-action/action/visitRecordsShare/delete", params);
// 我同行人员
export const getMyPartnerList = (params) => http.get("/jeecg-action/action/visitRecordsPartner/queryByRecordId", params);



// 查询创建的群组
export const getMyCreateGroupList = (params) => http.get("/jeecg-action/action/userGroup/getMyCreateGroupList", params);
// 查询所在的群组
export const getMyGroupList = (params) => http.get("/jeecg-action/action/userGroup/getMyGroupList", params);
// 查询全部的群组
export const getAllGroupList = (params) => http.get("/jeecg-action/action/group/list", params);



// 通过groupId查询任务下达
export const queryByGroupId = (params) => http.get("/jeecg-action/action/visitGroupTask/queryByGroupId", params);
// 营销动作列表  startDate  endDate  pageNo  visitType(0123456789)  state(0临时保存1已提交)  auditStatus(0未审核1已审核‘’全部)
export const getMyMarketActionList = (params) => http.get("/jeecg-action/action/visitStat/getMyVisitMoreSearch", params);
// 营销动作统计  startDate  endDate 
export const getAllMarketActionStatics = (params) => http.get("/jeecg-action/action/visitStat/getMyVisitMoreStat", params);
// 我的活动统计  date: 2025-04
export const getMyMarketActionStatics = (params) => http.get("/jeecg-action/action/visitStat/getMyVisitStat", params);
// 群营销动作统计  groupId: 302  date: 2025-04
export const getGroupMarketActionStatics = (params) => http.get("/jeecg-action/action/visitStat/getGroupVisitStat", params);
// 查询
export const getPeopleMarketActionList = (params) => http.get("/jeecg-action/action/visitRecords/queryVisitList", params);



// 我的活动地图
export const getMyActionMap = (params) => http.get("/jeecg-action/action/visitRecords/queryMyVisitRecordsMap", params);



// 我记录别人同行 pageNo 
export const meRecordOther = (params) => http.get("/jeecg-action/action/visitRecordsPartner/queryMineSavePartnerRecordByPage", params);
// 别人记录我同行
export const otherRecordMe = (params) => http.get("/jeecg-action/action/visitRecordsPartner/queryOtherSavePartnerRecordByPage", params);
// 我分享别人
export const meShareOther = (params) => http.get("/jeecg-action/action/visitRecordsShare/queryMineShareRecordByPage", params);
// 别人分享我
export const otherShareMe = (params) => http.get("/jeecg-action/action/visitRecordsShare/queryOtherShareRecordByPage", params);



// 查询拜访记录  clientName客户姓名  address地址  visitType  startDate  endDate  pageNo  orderByFlag时间排序
export const queryVisitRecordList = (params) => http.get("/jeecg-action/action/visitRecords/listByParamFreeOrderBy", params);



// 阶段任务下达
export const getMissionAssignList = (params) => http.get("/jeecg-action/action/visitGroupTask/list", params);
// 阶段任务添加
export const addMissionAssign = (params) => http.post("/jeecg-action/action/visitGroupTask/add", params);
// 阶段任务编辑
export const editMissionAssign = (params) => http.post("/jeecg-action/action/visitGroupTask/edit", params);
// 阶段任务id查询
export const getMissionAssignListById = (params) => http.get("/jeecg-action/action/visitGroupTask/queryById", params);
// 阶段任务删除
export const delMissionAssign = (params) => http.delete("/jeecg-action/action/visitGroupTask/delete", params);



// 获取我可以审核或我创建的组
export const getMyReviewGroupList = (params) => http.get("/jeecg-action/action/userGroup/getMyReviewGroupList", params);
// 督查授权查询
export const getMissionGroupAuthList = (params) => http.get("/jeecg-action/action/groupAuthorize/list", params);
// 督查授权添加
export const addMissionGroupAuth = (params) => http.post("/jeecg-action/action/groupAuthorize/add", params);
// 编辑
export const editMissionGroupAuth = (params) => http.post("/jeecg-action/action/groupAuthorize/edit", params);
// id查询
export const getMissionGroupAuthListById = (params) => http.get("/jeecg-action/action/groupAuthorize/queryById", params);
// 督查授权删除
export const delMissionGroupAuth = (params) => http.delete("/jeecg-action/action/groupAuthorize/delete", params);
// 获取群人员
export const getUserGroupList = (params) => http.get("/jeecg-action/action/userGroup/getUserGroupList", params);



// 休息备案查询
export const getRestRegister = (params) => http.get("/jeecg-action/action/visitRestFiling/list", params);
// 休息备案添加
export const addRestRegister = (params) => http.post("/jeecg-action/action/visitRestFiling/add", params);
// 休息备案编辑
export const editRestRegister = (params) => http.post("/jeecg-action/action/visitRestFiling/edit", params);
// 休息备案id查询
export const getRestRegisterById = (params) => http.get("/jeecg-action/action/visitRestFiling/queryById", params);
// 休息备案删除
export const delRestRegister = (params) => http.delete("/jeecg-action/action/visitRestFiling/delete", params);
// 查询休息备案审核列表
export const queryReviewPageList = (params) => http.get("/jeecg-action/action/visitRestFiling/queryReviewPageList", params);
// 审核休息备案
export const validRestFiling = (params) => http.post("/jeecg-action/action/visitRestFiling/review", params);


// 住宿补贴查询
export const getStayInfo = (params) => http.get("/jeecg-action/action/visitStayInfo/list", params);
// 住宿补贴添加
export const addStayInfo = (params) => http.post("/jeecg-action/action/visitStayInfo/add", params);
// 住宿补贴id查询
export const getStayInfoById = (params) => http.get("/jeecg-action/action/visitStayInfo/queryById", params);
// 住宿补贴删除
export const delStayInfo = (params) => http.delete("/jeecg-action/action/visitStayInfo/delete", params);


// 添加报销
export const addReimburse = (params) => http.post("/jeecg-action/action/visitReimburse/addBatch", params);
// 查询报销记录
export const reimburseList = (params) => http.get("/jeecg-action/action/visitReimburse/list", params);
// 删除报销记录id
export const delActionReimburseById = (params) => http.delete("/jeecg-action/action/visitReimburse/delete", params);


