<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'default',
        navigationBarTitleText: '',
      },
    }
</route>

<template>
    <PageLayout :navbarShow="false" :isloginPage="true" :showFab="false">
        <view class="container">
            <view class="header">
                <text class="title">隐私政策</text>
                <!-- <text class="date">生效日期：2024年1月1日</text> -->
            </view>

            <scroll-view class="content" scroll-y="true">
                <view class="section">
                    <text class="section-title">1. 隐私政策概述</text>
                    <text class="section-content">
                        智能管理系统（以下简称"我们"）深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们承诺严格遵守相关法律法规，采取相应的安全保护措施，保护您的个人信息。
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">2. 信息收集范围</text>
                    <text class="section-content">
                        我们可能收集以下类型的个人信息：
                        • 账户信息：用户名、密码、手机号码等
                        • 设备信息：设备型号、操作系统、设备标识符等
                        • 使用信息：登录记录、操作日志、功能使用情况等
                        • 位置信息：GPS定位、IP地址等（需要用户授权）
                        • 其他信息：您主动提供的其他个人信息
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">3. 信息使用目的</text>
                    <text class="section-content">
                        我们收集和使用您的个人信息仅用于以下目的：
                        • 提供和优化产品服务
                        • 用户身份验证和账户安全
                        • 客户服务和技术支持
                        • 产品功能改进和开发
                        • 法律法规要求的其他用途
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">4. 信息共享原则</text>
                    <text class="section-content">
                        我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：
                        • 获得您的明确同意
                        • 法律法规要求或政府部门要求
                        • 为保护我们的权利、财产或安全
                        • 为保护其他用户或公众的权利、财产或安全
                        • 与我们的关联公司、服务提供商或代理人共享
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">5. 信息安全保护</text>
                    <text class="section-content">
                        我们采用多种安全措施保护您的个人信息：
                        • 数据加密：对敏感信息进行加密存储和传输
                        • 访问控制：严格限制访问个人信息的人员
                        • 安全审计：定期进行安全评估和漏洞扫描
                        • 备份措施：建立完善的数据备份和恢复机制
                        • 人员培训：对员工进行隐私保护培训
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">6. 您的权利</text>
                    <text class="section-content">
                        您对自己的个人信息享有以下权利：
                        • 知情权：了解我们收集、使用个人信息的情况
                        • 决定权：决定是否提供个人信息
                        • 查询权：查询您的个人信息
                        • 更正权：更正或补充您的个人信息
                        • 删除权：要求删除您的个人信息
                        • 投诉权：对我们的个人信息处理行为进行投诉
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">7. 信息保存期限</text>
                    <text class="section-content">
                        我们仅在实现信息收集目的所需的期限内保留您的个人信息：
                        • 账户信息：在您的账户存续期间
                        • 日志信息：一般保存不超过12个月
                        • 其他信息：根据法律要求或业务需要确定保存期限
                        • 超过保存期限后，我们将及时删除相关信息
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">8. 未成年人保护</text>
                    <text class="section-content">
                        我们非常重视未成年人的个人信息保护：
                        • 不主动收集未成年人的个人信息
                        • 如发现收集了未成年人信息，将立即删除
                        • 建议未成年人在监护人指导下使用我们的服务
                        • 如您是未成年人的监护人，请监督其使用我们的服务
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">9. 政策更新</text>
                    <text class="section-content">
                        我们可能会不定期更新本隐私政策：
                        • 重大变更将通过显著方式通知您
                        • 您可以随时查阅最新版本的隐私政策
                        • 继续使用我们的服务即表示同意更新后的政策
                        • 如不同意更新，您可以停止使用我们的服务
                    </text>
                </view>

                <view class="section">
                    <text class="section-title">10. 联系我们</text>
                    <text class="section-content">
                        如您对本隐私政策有任何疑问或建议，请联系我们：
                        • 邮箱：xxx.com
                        • 电话：191xxxxxxxx
                        • 地址：广西壮族自治区南宁市西乡塘区神农大厦
                        • 我们将在收到您的反馈后尽快回复
                    </text>
                </view>
            </scroll-view>

            <view class="footer">
                <button class="agree-btn" @click="goBack">我已阅读并同意</button>
            </view>
        </view>
    </PageLayout>
</template>

<script>
export default {
    methods: {
        goBack() {
            uni.navigateBack();
        }
    }
}
</script>

<style scoped>
.container {
    min-height: 100vh;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
}

.header {
    background: #ffffff;
    padding: 10px;
    border-bottom: 1px solid #e0e0e0;
    position: sticky;
    top: 0;
    z-index: 10;
}

.title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    text-align: center;
    display: block;
    margin-bottom: 5px;
}

.date {
    font-size: 12px;
    color: #666;
    text-align: center;
    display: block;
}

.content {
    flex: 1;
    padding: 20px;
}

.section {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 80%;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #4CAF50;
    margin-bottom: 15px;
    display: block;
}

.section-content {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    text-align: justify;
    white-space: pre-line;
}

.footer {
    background: #ffffff;
    padding: 20px;
    border-top: 1px solid #e0e0e0;
}

.agree-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    color: #ffffff;
    border: none;
    padding: 6px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .header {
        padding: 15px;
    }

    .content {
        padding: 15px;
    }

    .section {
        padding: 15px;
    }

    .footer {
        padding: 15px;
    }
}
</style>