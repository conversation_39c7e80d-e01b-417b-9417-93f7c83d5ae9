<template>
    <wd-cell :title="label" :title-width="'80px'" :prop="prop">
        <view class="flex">
            <!-- <view>
                <view v-for="(item, index) in defaultData" :key="index">
                    {{ item.label }}<wd-icon name="delete" size="1rem" color="red" @click="delPeople(item)"></wd-icon>
                </view>
            </view> -->
            <view class="mr2">{{ defaultData[0]?.label }}</view>
            <wd-button type="warning" custom-class="w50%" @click="toSelPartner" size="small"
                :disabled="disabled">选择群</wd-button>
        </view>
    </wd-cell>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'

const router = useRouter()

const props = defineProps({
    prop: {
        type: String,
        default: ''
    },
    name: {
        type: String,
        default: ''
    },
    label: {
        type: String,
        default: ''
    },
    defaultData: {
        type: Array,
        default: []
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const toSelPartner = () => {
    console.log(props.defaultData);

    router.push({
        name: 'marketActionChooseGroupFindAll',
        params: {
            defaultData: JSON.stringify(props.defaultData),
            name: props.name
        }
    })
}

</script>

<style lang="scss" scoped></style>