<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
    <PageLayout :navTitle="customTitle" :showFab="false">
        <scroll-view scroll-y>
            <customForm :formConfig="formConfig" :formData="formData" :rules="rules" @setFormData="setFormData"
                @customSetFormData="customSetFormData" @submit="submit" btnText1="提交" :disabledBtn="action == 'check'">
            </customForm>
        </scroll-view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onHide, onLoad, onUnload, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify, dayjs } from 'wot-design-uni'
import customForm from '@/components/myForm/customForm.vue'
import { formConfig, rules } from './marketActionLiveSubsidyFormConfig'
import { addStayInfo, getStayInfoById } from '@/service/marketAction/marketAction'
import { getDict } from '@/service/index/foo'
import { getAllGroupList, getUserTenantPageList } from '@/service/marketAction/marketAction'

const toast = useToast()

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo


const action = ref('')
const customTitle = computed(() => {
    if (action.value == 'add') {
        return '添加住宿补贴'
    } else if (action.value == 'edit') {
        return '修改住宿补贴'
    } else if (action.value == 'check') {
        return '查看住宿补贴'
    }
})

const formData = ref({})

// 原组件表单值改变后可进行其他操作--------------------------
const setFormData = (e) => {
    console.log(e);
    if (e.name == 'userStayType') {
        if (e.data.value == 1) {
            getDict('stayTypeFixed').then((res: any) => {
                console.log(res);
                formConfig.value[0].form[1].columns = ref(res.result)
            })
        }
        if (e.data.value == 2) {
            getDict('stayTypeTeam').then((res: any) => {
                console.log(res);
                formConfig.value[0].form[1].columns = ref(res.result)
            })
        }
    }
    // if (e.name == 'userId') {
    //     formData.value.userName = e.data.selectedItems.realname
    // }
}
// 自定义组件表单值改变后可进行其他操作--------------------------
const customSetFormData = (e) => {
    console.log(e);
}

// 设置字典
const setDictColumns = (dictText, ziduan) => {
    getDict(dictText).then((res: any) => {
        formConfig.value[0].form.find(item => item.name == ziduan).columns = res.result.map(item => ({
            value: Number(item.value),
            label: item.label
        }))
    })
}

onLoad((opts) => {
    // 人员类型
    setDictColumns('userType', 'userStayType')
    // // 群组
    // getAllGroupList({
    //     znkqUserId: userInfo.znkqId,
    //     pageSize: 9999
    // }).then((res: any) => {
    //     console.log(res);
    //     if (res.code == 200) {
    //         formConfig.value[0].form.find(item => item.name == 'groupId').columns = res.result.records.map(item => ({
    //             ...item,
    //             value: Number(item.id),
    //             label: item.name
    //         }))
    //     }
    // })
    // 人员
    // getUserTenantPageList({
    //     pageSize: 9999,
    //     userTenantStatus: 1
    // }).then((res: any) => {
    //     console.log(res);
    //     if (res.code == 200) {
    //         formConfig.value[0].form.find(item => item.name == 'userId').columns = res.result.records.map(item => ({
    //             ...item,
    //             value: Number(item.id),
    //             label: item.realname
    //         }))
    //     }
    // })


    console.log(opts);
    action.value = opts.action

    // // 编辑
    // if (opts.action == 'edit') {
    //     getStayInfoById({ id: opts.id }).then((res: any) => {
    //         console.log(res);
    //         if (res.code == 200) {
    //             res.result.duration = `${res.result.duration / (1000 * 3600 * 24)}天`
    //             res.result.startTime = new Date(`${res.result.startTime}T00:00:00`).getTime()
    //             res.result.endTime = new Date(`${res.result.endTime}T00:00:00`).getTime()
    //             formData.value = res.result
    //         }
    //     })
    //     setFormDisAbled(false)
    // }
    // // 查看
    // if (opts.action == 'check') {
    //     getStayInfoById({ id: opts.id }).then((res: any) => {
    //         console.log(res);
    //         if (res.code == 200) {
    //             res.result.duration = `${res.result.duration / (1000 * 3600 * 24)}天`
    //             res.result.startTime = new Date(`${res.result.startTime}T00:00:00`).getTime()
    //             res.result.endTime = new Date(`${res.result.endTime}T00:00:00`).getTime()
    //             formData.value = res.result
    //         }
    //     })
    //     setFormDisAbled(true)
    //     console.log(formConfig.value);
    // } else {
    //     setFormDisAbled(false)
    // }

    // 选择人员（住宿补贴）
    uni.$on('choosePeoples', (e) => {
        console.log(e);
        formData.value.custom_selectPeoples = e
        formData.value.userId = e[0]?.id
        formData.value.userName = e[0]?.name
    })
    uni.$on('chooseGroups', (e) => {
        console.log(e);
        formData.value.custom_selectGroups = e
        formData.value.groupId = e[0]?.id
        // formData.value.userName = e[0]?.name
    })
    // 删除人员
    // uni.$on('deletePeoples', (e) => {
    //     // console.log(e);
    //     formData.value.custom_selectPeoples = formData.value.custom_selectPeoples.filter(item => item.id != e.id)
    // })
})

onUnload(() => {
    uni.$off('choosePeoples')
    // uni.$off('deletePeoples')
})


const setFormDisAbled = (flag) => {
    formConfig.value[0].form = formConfig.value[0].form.map(item => ({
        ...item,
        disabled: flag
    }))
}

const submit = () => {
    // console.log(formData.value);
    // return

    if (action.value == 'add') {
        let data = JSON.parse(JSON.stringify(formData.value))
        if (data.addByGroupOrPeople == 1) {
            data.userId = ''
        }
        if (data.addByGroupOrPeople == 2) {
            data.groupId = ''
        }
        // 处理人员信息
        console.log(data);

        addStayInfo(data).then((res: any) => {
            console.log(res);
            if (res.code == 200) {
                router.back()
                uni.$emit('refreshList', '添加成功！')
            } else {
                toast.error(res.message)
            }
        })
    }
}

</script>

<style lang="scss" scoped></style>