<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '选择客户',
        navigationStyle: 'custom',
      },
    }
</route>

<template>
  <PageLayout navTitle="选择客户">
    <wd-search v-model="searchValue" hide-cancel @search="search" @clear="clear" placeholder="输入姓名搜索" />
    <view v-if="showIndexBar" class="wraper">
      <wd-index-bar sticky>
        <view v-for="item in groupedData" :key="item.index">
          <wd-index-anchor :index="item.index" />
          <wd-cell border clickable v-for="(sub, index) in item.data" :key="index" :title="sub.clientName"
            @click="handleClick(sub)"></wd-cell>
        </view>
      </wd-index-bar>
    </view>
    <view v-else>
      <wd-cell border clickable v-for="(item, index) in searchData" :key="index" :title="item.clientName"
        @click="handleClick(item)"></wd-cell>
    </view>
  </PageLayout>
</template>

<script lang="ts" setup>
import { pinyin } from 'pinyin-pro';
import { getExistClients } from '@/service/marketAction/marketAction';
import { onLoad } from '@dcloudio/uni-app';

import { useRouter } from '@/plugin/uni-mini-router';
const router = useRouter()

import { useUserStore } from '@/store'
const userInfo = useUserStore().userInfo

const originData = ref([])

const showIndexBar = ref(true)

const searchValue = ref('')
const search = (e) => {
  console.log(e);
  if (e.value) {
    showIndexBar.value = false
    console.log(originData.value);
    searchData.value = originData.value.filter(item => item.clientName.indexOf(e.value) != -1)
  } else {
    showIndexBar.value = true
    searchData.value = []
  }
}
const clear = (e) => {
  showIndexBar.value = true
}

const getFirstLetter = (text: string) => {
  if (!text) {
    return '#';
  }

  // 中文转拼音首字母
  if (/^[\u4e00-\u9fa5]/.test(text)) {
    const firstChar = pinyin(text.charAt(0), {
      pattern: 'first', // 只获取首字母
      toneType: 'none', // 不保留声调
    });
    return firstChar.toUpperCase();
  }

  // 英文取首字母
  const firstChar = text.charAt(0).toUpperCase();
  return /^[A-Z]$/.test(firstChar) ? firstChar : '#';
}

const groupByFirstLetter = (arr, key = "clientName") => {
  // const item = arr[key]
  const groupedDict = arr.reduce((result, item) => {
    const firstLetter = getFirstLetter(item[key]);
    (result[firstLetter] ||= []).push(item);
    return result;
  }, {} as Record<string, string[]>);

  // 转换为目标结构并排序（A-Z 在前，# 在最后）
  return Object.entries(groupedDict)
    .map(([index, data]) => ({ index, data }))
    .sort((a, b) => {
      if (a.index === '#') return 1;  // # 排在最后
      if (b.index === '#') return -1;
      return a.index.localeCompare(b.index);
    });
}

const groupedData = ref([])
onLoad(() => {
  getExistClients({
    userId: userInfo.userid,
  }).then((res: any) => {
    console.log(res);
    if (res.code == 200) {
      originData.value = res.result
      groupedData.value = groupByFirstLetter(res.result.filter(item => item.clientName));
      console.log(groupedData.value);
    }
  })
})

const searchData = ref([])

const handleClick = (e) => {
  console.log(e);
  router.back()
  uni.$emit('chooseClient', e)
}

</script>

<style lang="scss" scoped>
.wraper {
  height: calc(100vh - var(--window-top));
  height: calc(100vh - var(--window-top) - constant(safe-area-inset-bottom));
  height: calc(100vh - var(--window-top) - env(safe-area-inset-bottom) - 100px);
}

// :v-deep .wd-search__cover {
//   height: 10px !important;
// }</style>