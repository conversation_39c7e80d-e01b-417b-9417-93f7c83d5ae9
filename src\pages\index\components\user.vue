<template>
    <!-- 个人信息卡片 -->
    <view class="bg-gradient mx-2.5 mt-12 rounded-3xl p-4 shadow-lg">
        <view class="flex items-center">
            <image class="w-10 h-10 rounded-full border-4 border-white shadow-md" src="/static/chat/emoji.png"
                mode="aspectFill"></image>
            <view class="ml-6 flex-1">
                <text class="text-xl font-bold text-white block mb-1">{{ userInfo.realname }}</text>
                <text class="text-sm text-purple-100 block">员工</text>
            </view>
        </view>
    </view>

    <!-- 功能列表 -->
    <view class="mx-2.5 mt-4">
        <view class="bg-white rounded-2xl shadow-sm overflow-hidden pb3">
            <view class="flex items-center px-4 pt-4 border-b border-gray-100" v-for="(item, index) in menuList"
                :key="index" @tap="handleCell(item)">
                <view class="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
                    :style="{ backgroundColor: item.bgColor }">
                    <image class="w-6 h-6" :src="item.icon" mode="aspectFit"></image>
                </view>
                <view class="flex-1">
                    <text class="text-base font-semibold text-gray-900 block mb-1">{{ item.title }}</text>
                    <text class="text-sm text-gray-500">{{ item.desc }}</text>
                </view>
                <!-- <image class="w-5 h-5" src="/static/icons/arrow-right.svg" mode="aspectFit"></image> -->
                <wd-icon name="arrow-right" size="22px"></wd-icon>
            </view>
        </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="mx-2.5 mt-4">
        <view class="flex flex-col gap-4">
            <view class="bg-white rounded-2xl shadow-sm transition-all duration-300 active:scale-98 active:shadow-lg"
                @click="clearCache">
                <view class="flex items-center justify-center p-3">
                    <!-- <image class="w-6 h-6 mr-3" src="/static/icons/refresh.svg" mode="aspectFit"></image> -->
                    <wd-icon name="keyboard-delete" size="20px" class="mr1"></wd-icon>
                    <text class="font-size-4 font-semibold text-blue-600">清除缓存</text>
                </view>
            </view>
            <view class="bg-white rounded-2xl shadow-sm transition-all duration-300 active:scale-98 active:shadow-lg"
                @click="exit">
                <view class="flex items-center justify-center p-3">
                    <!-- <image class="w-6 h-6 mr-3" src="/static/icons/logout.svg" mode="aspectFit"></image> -->
                    <wd-icon name="close-outline" size="20px" class="mr1"></wd-icon>
                    <text class="font-size-4 font-semibold text-red-600">退出登录</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onBeforeUnmount } from 'vue'
import { onLaunch, onShow, onHide, onLoad, onReady } from '@dcloudio/uni-app'
import { useToast, useMessage, useNotify } from 'wot-design-uni'
import { useRouter } from '@/plugin/uni-mini-router'
import { useUserStore } from '@/store/user'

import { refleshCache, logoutSystem } from '@/service/index/foo'
import { useGlobalStore } from '@/store'

const userInfo = useUserStore().userInfo
const userStore = useUserStore()
const useGlobal = useGlobalStore()
const toast = useToast()
const router = useRouter()
const message = useMessage()

const menuList = ref([
    // {
    //     icon: '/static/icons/user.svg',
    //     title: '个人信息',
    //     desc: '编辑个人资料和设置',
    //     bgColor: '#E3F2FD'
    // },
    {
        icon: '/static/index/safe.png',
        title: '账号安全',
        desc: '密码、手机号等安全设置',
        bgColor: '#F3E5F5'
    },
    // {
    //     icon: '/static/icons/notification.svg',
    //     title: '消息通知',
    //     desc: '推送和通知设置',
    //     bgColor: '#E8F5E8'
    // },
    {
        icon: '/static/index/help.png',
        title: '帮助中心',
        desc: '常见问题和客服支持',
        bgColor: '#FFF3E0'
    },
    // {
    //     icon: '/static/icons/about.svg',
    //     title: '关于应用',
    //     desc: '版本信息和隐私政策',
    //     bgColor: '#FCE4EC'
    // }
])

const onMenuTap = (item) => {
    console.log(item);
}

const clearCache = () => {

    uni.showModal({
        title: '提示',
        content: '确定清除缓存吗？',
        success: function (res) {
            if (res.confirm) {
                refleshCache().then((res: any) => {
                    console.log(res);
                    if (res.code == 0) {
                        toast.success('清除成功!')
                    }
                })
            } else if (res.cancel) {
                console.log('用户点击取消');
            }
        }
    });
    // message
    //     .confirm({
    //         title: '提示',
    //         msg: '确定清除缓存吗？',
    //     })
    //     .then(() => {
    //         refleshCache().then((res: any) => {
    //             console.log(res);
    //             if (res.code == 0) {
    //                 toast.success('清除成功!')
    //             }
    //         })
    //     })
}
const exit = () => {
    uni.showModal({
        title: '提示',
        content: '确定退出吗？',
        success: function (res) {
            if (res.confirm) {
                logoutSystem().then((res: any) => {
                    console.log(res);
                })
                userStore.clearUserInfo()
                useGlobal.clearMenuInfo()
                useGlobal.clearFabs()
                useGlobal.clearMarketActionAuditStatus()
                useGlobal.clearMarketActionVisitState()
                router.replaceAll({ name: 'login' })
            } else if (res.cancel) {
                console.log('用户点击取消');
            }
        }
    });
    // message
    //     .confirm({
    //         title: '提示',
    //         msg: '确定退出吗？',
    //     })
    //     .then(() => {
    //         logoutSystem().then((res: any) => {
    //             console.log(res);
    //         })
    //         userStore.clearUserInfo()
    //         useGlobal.clearMenuInfo()
    //         useGlobal.clearFabs()
    //         useGlobal.clearMarketActionAuditStatus()
    //         useGlobal.clearMarketActionVisitState()
    //         router.replaceAll({ name: 'login' })
    //     })
}

const handleCell = (item) => {
    switch (item.title) {
        case '账号安全111':
            router.push({ name: 'marketActionAddRecordMap' })
            break
        case '帮助中心222':
            router.push({ name: 'marketActionAddRecordMap' })
            break
        default:
            toast.show('功能暂未开发~')
    }
}

</script>

<style lang="scss" scoped>
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>