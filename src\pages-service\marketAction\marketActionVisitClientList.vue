<route lang="json5" type="page">
    {
      layout: 'default',
      style: {
        navigationBarTitleText: '客户信息备忘录',
        navigationStyle: 'custom',
      },
    }
</route>
<template>
    <PageLayout navTitle="客户信息备忘录" :backRouteName="backRouteName" :routeMethod="routeMethod">
        <wd-drop-menu>
            <wd-drop-menu-item v-model="value1" :options="option1" @change="handleChange1" />
            <wd-drop-menu-item v-model="value2" :options="option2" @change="handleChange2" />
            <wd-drop-menu-item v-model="value3" :options="option3" @change="handleChange3" />
        </wd-drop-menu>
        <view class="pl4 pr4 pb2 flex bg-white">
            <wd-calendar type="daterange" v-model="visitRecordDate" @confirm="confirmVisitRecordDate" use-default-slot
                custom-class="w100%" :min-date="minDate" :max-date="maxDate">
                <wd-button icon="calendar" plain custom-class="w100%">
                    日期 : {{ `${dayjs(visitRecordDate[0]).format('YYYY-MM-DD')} 至
                    ${dayjs(visitRecordDate[1]).format('YYYY-MM-DD')}` }}
                </wd-button>
            </wd-calendar>
        </view>
        <view class="pl2 pr2 pb2 flex bg-white">
            <wd-search v-model="searchClientName" @search="searchByText" hide-cancel custom-class="w50%"
                placeholder="输入客户名称搜索" @clear="searchByText" />
            <wd-search v-model="searchAddress" @search="searchByText" hide-cancel custom-class="w50%"
                placeholder="输入地点搜索" @clear="searchByText" />
        </view>
        <scroll-view scroll-y @scrolltolower="scrolltolower">
            <wd-collapse ref="collapse" v-model="valueColl" @toggleAll="">
                <wd-collapse-item v-for="(item, index) in clientList" :key="index" :name="String(index)"
                    :custom-class="index % 2 == 0 ? 'bg-#e7e7e7' : 'bg-white'">
                    <template #title="{ expanded, disabled, isFirst }">
                        <view class="flex flex-justify-between flex-items-center">
                            <view class="w100%">
                                <view class="flex flex-justify-between mb2">
                                    <view>客户:{{ item.clientName }}</view>
                                    <view>日期:{{ dayjs(new Date(item.startTime).getTime()).format('YYYY-MM-DD') }}</view>
                                </view>
                                <view class="flex flex-justify-between mb2">
                                    <view>联系方式:{{ item.clientPhone }}</view>
                                    <view>类型:{{ item.visitType_dictText }}</view>
                                </view>
                                <view class="flex flex-justify-between">
                                    <view>地点:{{ item.address }}</view>
                                </view>
                            </view>
                            <view class="ml3">
                                <wd-icon :name="expanded ? 'arrow-up' : 'arrow-down'" size="16px"></wd-icon>
                            </view>
                        </view>
                    </template>
                    <view>
                        <view class="font-size-4">描述 : {{ item.interviewDescription }}</view>
                        <view class="">
                            <wd-button custom-class="w100% mt2 mb2" plain size="small"
                                @click="toDetail(item.id)">查看详情</wd-button>
                        </view>
                    </view>
                </wd-collapse-item>
            </wd-collapse>
            <wd-loadmore custom-class=" bg-#edf5ea" :state="loadMoreState" @reload="reloadData" />
        </scroll-view>
    </PageLayout>
</template>

<script lang="ts" setup>
import { dayjs } from 'wot-design-uni';
import { onLoad } from '@dcloudio/uni-app'
import { CollapseInstance } from 'wot-design-uni/components/wd-collapse/types';
import { queryVisitRecordList } from '@/service/marketAction/marketAction';
import { getDict } from '@/service/index/foo'

import { useRouter } from '@/plugin/uni-mini-router'
const router = useRouter()
const backRouteName = ref('index')
const routeMethod = ref('replaceAll')

const minDate = Date.now() - 3 * 31536000000
const maxDate = Date.now()

// 折叠面板ref实例
const collapse = ref<CollapseInstance>()

// 日期(筛选)
const visitRecordDate = ref<number[]>([Date.now() - 90 * 24 * 3600 * 1000, Date.now()])
const confirmVisitRecordDate = (e) => {
    console.log(e);
    resetPageNo()
    getClientList()
}

const searchClientName = ref('')
const searchAddress = ref('')
const pageNo = ref(1)
const pageSize = ref(10)

// 按钮1拜访类型
const value1 = ref<number>(0)
const option1 = ref<Record<string, any>[]>([])
getDict('visitType').then((res: any) => {
    option1.value = res.result.map(item => ({
        value: Number(item.value),
        label: item.label
    }))
    option1.value.unshift({
        value: 0,
        label: '全部类型'
    })
})
const handleChange1 = (e) => {
    resetPageNo()
    getClientList()
}
// 按钮2(展开收起)
const value2 = ref<number>(2)
const option2 = ref<Record<string, any>[]>([
    { label: '展开所有', value: 1 },
    { label: '收起所有', value: 2 }
])
const handleChange2 = (e) => {
    console.log(e)
    if (e.value == 1) {
        collapse.value.toggleAll(true)
    } else if ((e.value == 2)) {
        collapse.value.toggleAll(false)
    }
}
// 按钮3(时间排序)
const value3 = ref<number>(1)
const option3 = ref<Record<string, any>[]>([
    // { label: '默认排序', value: 1 },
    { label: '时间降序', value: 1 },
    { label: '时间升序', value: 0 },
])
const handleChange3 = (e) => {
    console.log(e)
    resetPageNo()
    getClientList()
}

// ids查询
const valudIds = ref('')

// 搜索框
const searchByText = () => {
    resetPageNo()
    getClientList()
}

// 折叠值
const valueColl = ref<string[]>(['item1'])
// 加载状态显示
const loadMoreState = ref<string>('loading')
// 列表内容
const clientList = ref([])
// 重新加载
const reloadData = () => {
    // clientList.value = clientList.value + 10
}
// 滚动到底部
const scrolltolower = () => {
    if (loadMoreState.value == 'finished') {
        return
    }
    console.log(123);
    queryVisitRecordList({
        clientName: searchClientName.value,
        address: searchAddress.value,
        visitType: value1.value == 0 ? '' : value1.value,
        startDate: dayjs(visitRecordDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(visitRecordDate.value[1]).format('YYYY-MM-DD'),
        pageNo: pageNo.value + 1,
        pageSize: pageSize.value,
        orderByFlag: value3.value,
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            if (res.result.records.length == 0) {
                loadMoreState.value = 'finished'
                return
            } else {
                clientList.value = [...clientList.value, ...res.result.records]
                pageNo.value = pageNo.value + 1
            }

        }

    })
}

onLoad((opts) => {
    console.log(opts);
    if (opts) {
        console.log('setopts');
        opts.date ? visitRecordDate.value = JSON.parse(opts.date) : ''
        opts.visitType ? value1.value = Number(opts.visitType) : ''
        opts.ids ? valudIds.value = JSON.stringify(JSON.parse(opts.ids)) : ''
        opts.backRouteName ? backRouteName.value = opts.backRouteName : ''
        opts.routeMethod ? routeMethod.value = opts.routeMethod : ''
    }
    getClientList()
})

const getClientList = () => {
    loadMoreState.value = 'loading'
    queryVisitRecordList({
        clientName: searchClientName.value,
        address: searchAddress.value,
        visitType: value1.value == 0 ? '' : value1.value,
        startDate: dayjs(visitRecordDate.value[0]).format('YYYY-MM-DD'),
        endDate: dayjs(visitRecordDate.value[1]).format('YYYY-MM-DD'),
        pageNo: pageNo.value,
        pageSize: pageSize.value,
        orderByFlag: value3.value,
        ids: valudIds.value
    }).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            clientList.value = res.result.records
            if (res.result.total < pageSize.value) {
                loadMoreState.value = 'finished'
            }
        }
    })
}

const resetPageNo = () => {
    pageNo.value = 1
}

const toDetail = (id) => {
    router.push({
        path: '/pages-service/marketAction/marketActionCheckSingleRecord',
        query: {
            id: id
        }
    })
}

</script>

<style lang="scss" scoped>
// ::v-deep .wd-search {
// padding: 0.5rem 0.25rem;
// padding-right: 0;
// }

::v-deep .wd-search.is-without-cancel {
    padding: 0;
}
</style>